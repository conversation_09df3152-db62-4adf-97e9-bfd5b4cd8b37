# Contact Selection Testing Guide

This guide provides comprehensive testing procedures for the contact selection functionality on both iOS and Android platforms, with special focus on iOS limited access scenarios.

## Overview

The contact selection feature supports:
- **iOS Limited Access**: Progressive contact selection where users can grant access to specific contacts
- **Android Full Access**: Traditional all-or-nothing contact permission
- **Cross-Platform Consistency**: Same UI and behavior across platforms
- **Cache Management**: Efficient contact loading and cache invalidation

## iOS Testing Procedures

### Test 1: Initial Contact Selection (iOS Limited Access)

**Objective**: Verify iOS limited access workflow works correctly

**Steps**:
1. Open the app and navigate to Import/Export screen
2. Tap "Select" button for Customer or Supplier
3. **Expected**: iOS system contact picker appears
4. Select 1-2 contacts from the system picker
5. Tap "Done" in the system picker
6. **Expected**: Contact selection bottom sheet appears with selected contacts
7. **Expected**: "Add More Contacts" button is visible at the top of the footer
8. **Expected**: Selected contacts appear in the list with proper names and phone numbers

**Success Criteria**:
- ✅ iOS system contact picker appears on first access
- ✅ Selected contacts appear in the bottom sheet
- ✅ "Add More Contacts" button is visible
- ✅ Contact information is displayed correctly

### Test 2: Progressive Contact Addition (iOS)

**Objective**: Verify users can add more contacts progressively

**Steps**:
1. Complete Test 1 to have some contacts already selected
2. Tap "Add More Contacts" button
3. **Expected**: iOS system contact picker appears again
4. Select 2-3 additional contacts
5. Tap "Done" in the system picker
6. **Expected**: Return to bottom sheet with updated contact list
7. **Expected**: New contacts are added to the existing list (not replaced)
8. **Expected**: Total contact count increases

**Success Criteria**:
- ✅ "Add More Contacts" triggers iOS contact picker
- ✅ New contacts are added to existing list
- ✅ No contacts are lost in the process
- ✅ Contact list updates immediately

### Test 3: Bottom Sheet Reopening (iOS)

**Objective**: Verify contact access persists across app sessions

**Steps**:
1. Complete Test 2 to have multiple contacts granted
2. Tap "Select Contacts" to close the bottom sheet
3. Immediately tap "Select" button again to reopen
4. **Expected**: Bottom sheet opens with all previously granted contacts
5. **Expected**: "Add More Contacts" button is still available
6. Tap "Add More Contacts" again
7. **Expected**: iOS contact picker allows selecting additional contacts

**Success Criteria**:
- ✅ Previously granted contacts are still available
- ✅ "Add More Contacts" continues to work
- ✅ No need to re-grant access to previously selected contacts
- ✅ Can continue adding more contacts

### Test 4: iOS App Restart Persistence

**Objective**: Verify contact access persists after app restart

**Steps**:
1. Complete Test 2 to have multiple contacts granted
2. Close the app completely (not just minimize)
3. Restart the app
4. Navigate to Import/Export and open contact selection
5. **Expected**: Previously granted contacts are still available
6. **Expected**: "Add More Contacts" still works for additional contacts

**Success Criteria**:
- ✅ Contact access persists after app restart
- ✅ No need to re-grant access to previously selected contacts
- ✅ Can still add more contacts after restart

## Android Testing Procedures

### Test 5: Android Permission Flow

**Objective**: Verify Android full access permission works correctly

**Steps**:
1. Install app on Android device (or clear app data)
2. Open Import/Export screen and tap "Select" button
3. **Expected**: Android permission dialog appears
4. Tap "Allow" to grant contacts permission
5. **Expected**: Contact selection bottom sheet appears with all contacts
6. **Expected**: All device contacts are immediately available

**Success Criteria**:
- ✅ Android permission dialog appears on first access
- ✅ All contacts are available immediately after granting permission
- ✅ No progressive selection needed
- ✅ Contact list is complete

### Test 6: Android Contact Selection

**Objective**: Verify normal contact selection works on Android

**Steps**:
1. Complete Test 5 to have contacts permission granted
2. Verify all contacts are displayed in the list
3. Select multiple contacts using checkboxes
4. Use search functionality to filter contacts
5. Tap "Clear All" to deselect all contacts
6. Select contacts again and tap "Select Contacts"

**Success Criteria**:
- ✅ All device contacts are displayed
- ✅ Multi-selection works correctly
- ✅ Search filters contacts properly
- ✅ Clear All functionality works
- ✅ Selection state is maintained correctly

### Test 7: Android Reopening Behavior

**Objective**: Verify consistent behavior when reopening on Android

**Steps**:
1. Complete Test 6 with some contacts selected
2. Close and reopen the contact selection bottom sheet
3. **Expected**: All contacts are still available
4. **Expected**: Previous selection state is maintained
5. **Expected**: No additional permission requests

**Success Criteria**:
- ✅ All contacts remain available
- ✅ No additional permission requests
- ✅ Selection state is maintained
- ✅ Consistent behavior across sessions

## Cross-Platform Testing

### Test 8: UI Consistency

**Objective**: Verify UI looks and behaves consistently across platforms

**Steps**:
1. Test the same workflow on both iOS and Android
2. Compare UI elements, spacing, and animations
3. Verify button placement and functionality
4. Check that animations and transitions are smooth

**Success Criteria**:
- ✅ UI design is consistent across platforms
- ✅ Button placement and sizing is consistent
- ✅ Animations work smoothly on both platforms
- ✅ Text and icons are properly aligned

### Test 9: Search Functionality

**Objective**: Verify search works consistently on both platforms

**Steps**:
1. Open contact selection on both platforms
2. Enter various search terms (names, phone numbers, emails)
3. Verify search results are accurate and immediate
4. Test special characters and edge cases
5. Clear search and verify full list returns

**Success Criteria**:
- ✅ Search works immediately as user types
- ✅ Results are accurate and relevant
- ✅ Search covers names, phone numbers, and emails
- ✅ Clearing search restores full list

### Test 10: Performance Testing

**Objective**: Verify performance with large contact lists

**Steps**:
1. Test on devices with 500+ contacts
2. Measure loading time for contact list
3. Test scrolling performance
4. Test search performance with large lists
5. Verify memory usage is reasonable

**Success Criteria**:
- ✅ Contact list loads within 2 seconds
- ✅ Scrolling is smooth with large lists
- ✅ Search results appear within 500ms
- ✅ Memory usage is stable

## Error Scenarios Testing

### Test 11: Permission Denied

**Objective**: Verify graceful handling of permission denial

**Steps**:
1. Open contact selection
2. Deny contacts permission when prompted
3. **Expected**: User-friendly error message appears
4. **Expected**: Option to open settings is provided
5. Tap "Open Settings" and grant permission manually
6. Return to app and retry contact selection

**Success Criteria**:
- ✅ Clear error message when permission denied
- ✅ Settings link works correctly
- ✅ App recovers gracefully after manual permission grant
- ✅ No app crashes or freezes

### Test 12: No Contacts Available

**Objective**: Verify handling of devices with no contacts

**Steps**:
1. Test on device with no contacts stored
2. Grant contacts permission
3. **Expected**: Empty state message appears
4. **Expected**: UI remains functional
5. Add a contact to the device
6. Refresh the contact list

**Success Criteria**:
- ✅ Appropriate empty state message
- ✅ UI doesn't break with empty contact list
- ✅ Can detect new contacts when added
- ✅ Refresh functionality works

## Regression Testing Checklist

After any changes to contact selection functionality:

- [ ] iOS limited access workflow
- [ ] iOS progressive contact addition
- [ ] iOS contact persistence across sessions
- [ ] Android full access permission
- [ ] Android contact selection and search
- [ ] Cross-platform UI consistency
- [ ] Performance with large contact lists
- [ ] Error handling for permission denied
- [ ] Empty state handling
- [ ] Contact cache invalidation
- [ ] Memory leak testing
- [ ] Battery usage impact

## Known Platform Differences

### iOS Specific Behaviors:
- System contact picker appears for limited access
- Users can progressively grant access to more contacts
- "Add More Contacts" button is essential for user experience
- Contact access persists across app sessions
- Limited access is the default behavior on iOS 14+

### Android Specific Behaviors:
- Traditional permission dialog (all-or-nothing)
- All contacts available immediately after permission grant
- "Add More Contacts" button is less critical
- Full contact list access is standard
- Permission model is simpler

### Shared Behaviors:
- Contact caching for performance
- Search functionality across all contact fields
- Multi-selection with checkboxes
- Consistent UI design and animations
- Error handling and empty states

## Troubleshooting Common Issues

### Issue: iOS contacts not updating after granting access
**Solution**: Check cache invalidation logic, ensure `forceRefresh` is used

### Issue: Android permission dialog not appearing
**Solution**: Verify permission is declared in AndroidManifest.xml

### Issue: Search not working properly
**Solution**: Check search logic covers all contact fields (name, phone, email)

### Issue: UI performance issues with large contact lists
**Solution**: Implement virtualization or pagination for very large lists

### Issue: Contact selection state not persisting
**Solution**: Verify BLoC state management and selection persistence logic
