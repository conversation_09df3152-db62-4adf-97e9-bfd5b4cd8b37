name: bill_book
description: "A new Flutter project."
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8

  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7

  # Networking & API
  dio: ^5.8.0+1
  dio_cache_interceptor: ^4.0.3 # API Response Caching
  connectivity_plus: ^6.1.4 # Network Connectivity Check

  # Authentication & Security
  flutter_secure_storage: ^9.2.4 # Secure Token Storage
  crypto: ^3.0.6 # Encryption utilities

  # Local Storage & Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.5.3
  sqflite: ^2.4.2 # For offline invoice storage

  # UI & Design
  flutter_screenutil: ^5.9.3 # Responsive design
  cached_network_image: ^3.4.1 # Image caching
  lottie: ^3.3.1 # Animations
  shimmer: ^3.0.0 # Loading effects
  flutter_svg: ^2.2.0 # SVG support
  google_fonts: ^6.2.1 # Custom fonts

  # PDF & Document Generation
  pdf: ^3.11.3 # PDF generation
  printing: ^5.14.2 # Printing support
  open_file: ^3.5.10 # Open files

  # File & Image Handling
  image_picker: ^1.1.2 # Camera/Gallery access
  file_picker: ^10.2.0 # File selection
  path_provider: ^2.1.5 # App directories
  image_cropper: ^9.1.0 # Image cropping

  # QR Code & Barcode
  qr_flutter: ^4.1.0 # QR code generation
  barcode_widget: ^2.0.4 # Barcode generation
  mobile_scanner: ^7.0.1 # QR/Barcode scanning

  # Sharing & Communication
  share_plus: ^11.0.0 # Native sharing
  url_launcher: ^6.3.1 # External links
  flutter_email_sender: ^7.0.0 # Email integration

  # Date & Time
  intl: ^0.20.2 # Internationalization

  # Permissions & Contacts
  permission_handler: ^12.0.1 # Device permissions
  flutter_contacts: ^1.1.9+2 # Device contacts access (modern alternative)

  # Charts & Analytics
  fl_chart: ^1.0.0 # Charts and graphs
  syncfusion_flutter_charts: ^30.1.38 # Advanced charts

  # Localization
  flutter_localizations:
    sdk: flutter

  # Dependency Injection
  get_it: ^8.0.3 # Service locator
  injectable: ^2.5.0 # DI annotations

  # Navigation
  auto_route: ^10.1.0+1 # Code generation routing

  # Form Handling & Validation
  flutter_form_builder: ^10.1.0 # Form builder
  form_builder_validators: ^11.2.0 # Form validators

  # Payment Integration
  razorpay_flutter: ^1.4.0 # Razorpay payments

  # Communication & Messaging
  # flutter_local_notifications: ^19.3.0 # Local notifications

  # Utilities
  logger: ^2.6.0 # Logging
  uuid: ^4.5.1 # UUID generation

  # Animation & UI Enhancement
  flutter_staggered_animations: ^1.1.1 # Staggered animations
  pull_to_refresh: ^2.0.0 # Pull to refresh

  # Data Formatting
  currency_text_input_formatter: ^2.3.0 # Currency formatting
  decimal: ^3.2.4 # Precise decimal calculations

  # Keyboard & Input
  flutter_keyboard_visibility: ^6.0.0 # Keyboard visibility

  easy_localization: ^3.0.7+1 # Simple i18n
  dartz: ^0.10.1
  el_tooltip: ^2.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
