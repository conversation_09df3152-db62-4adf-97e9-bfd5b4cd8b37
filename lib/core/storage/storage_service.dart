import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // Hive
  Future<void> hiveSet(String boxName, String key, dynamic value) async {
    final box = await Hive.openBox(boxName);
    await box.put(key, value);
  }

  Future<dynamic> hiveGet(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    return box.get(key);
  }

  Future<void> hiveRemove(String boxName, String key) async {
    final box = await Hive.openBox(boxName);
    await box.delete(key);
  }

  // SharedPreferences
  Future<void> prefsSetString(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  Future<String?> prefsGetString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  Future<void> prefsRemove(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  // SecureStorage
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  Future<void> secureSet(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  Future<String?> secureGet(String key) async {
    return await _secureStorage.read(key: key);
  }

  Future<void> secureRemove(String key) async {
    await _secureStorage.delete(key: key);
  }
}
