import 'package:equatable/equatable.dart';

class ApiResponse<T> extends Equatable {
  final T? data;
  final String message;
  final bool success;

  const ApiResponse({this.data, this.message = '', this.success = false});

  factory ApiResponse.success(T data, {String message = ''}) {
    return ApiResponse(data: data, message: message, success: true);
  }

  factory ApiResponse.error(String message) {
    return ApiResponse(message: message, success: false);
  }

  @override
  List<Object?> get props => [data, message, success];
}
