# Core Module

This folder contains foundational code and utilities shared across all apps in this project. Use this as a starting point for new projects to ensure consistency and reusability.

## Folder Structure

- **constants/**: App-wide constants (colors, text styles, etc.)
- **di/**: Dependency injection and service locator setup
- **error/**: Custom exceptions and error handling
- **helpers/**: Utility/helper functions (e.g., snackbar helpers)
- **models/**: Core data models (e.g., API response)
- **services/**: Core services (e.g., network, storage)
- **utils/**: General utilities (e.g., validators)
- **widgets/**: Reusable widgets (e.g., custom form fields)
- **analytics/**: Analytics service abstraction (e.g., Firebase Analytics)
- **background/**: Background task management (e.g., Workmanager)
- **extensions/**: Dart/Flutter extension methods (e.g., String, DateTime, BuildContext)
- **localization/**: Localization delegates, language files, and helpers
- **logging/**: Centralized logger setup
- **navigation/**: Routing setup (e.g., auto_route)
- **notifications/**: Push/local notification setup and helpers
- **router/**: Route definitions and guards
- **storage/**: Local/secure storage abstractions
- **theme/**: Theme data, light/dark mode, and theme utilities

## Usage

1. **Initialization**: In your app's `main.dart`, call the DI setup before running the app.
2. **Extending**: Add new services, utilities, or widgets to the appropriate subfolder.
3. **Importing**: Use imports like `import 'package:your_app/core/services/network_service.dart';` to access core functionality.

## Adding to New Apps

- Copy the entire `core` folder to your new project.
- Update your `pubspec.yaml` with all required dependencies.
- Call the DI setup in your app's entry point.
- Extend as needed for your new app's requirements.
