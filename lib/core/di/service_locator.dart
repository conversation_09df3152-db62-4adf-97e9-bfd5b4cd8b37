import 'package:dio/dio.dart';
import '../services/network_service.dart';
import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Register core services
  getIt.registerLazySingleton<Dio>(() => Dio());
  getIt.registerLazySingleton<NetworkService>(
    () => NetworkService(getIt<Dio>()),
  );

  // Register shared services
  // Example: getIt.registerLazySingleton(() => NetworkInfo());
  // Example: getIt.registerLazySingleton(() => SharedPreferences());
  // Example: getIt.registerLazySingleton(() => LocalStorage());
}

Future<void> resetServiceLocator() async {
  await getIt.reset();
}
