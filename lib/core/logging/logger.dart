import 'package:logger/logger.dart';

final Logger logger = Logger(
  printer: <PERSON><PERSON>rinter(
    methodCount: 2,
    errorMethodCount: 8,
    lineLength: 120,
    colors: true,
    printEmojis: true,
    dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
  ),
  level: Level.debug, // Change to Level.info or Level.error for production
);

void logInfo(dynamic message, {dynamic error, StackTrace? stackTrace}) {
  logger.i(message, error: error, stackTrace: stackTrace);
}

void logWarning(dynamic message, {dynamic error, StackTrace? stackTrace}) {
  logger.w(message, error: error, stackTrace: stackTrace);
}

void logError(dynamic message, {dynamic error, StackTrace? stackTrace}) {
  logger.e(message, error: error, stackTrace: stackTrace);
}

void logDebug(dynamic message, {dynamic error, StackTrace? stackTrace}) {
  logger.d(message, error: error, stackTrace: stackTrace);
}
