extension StringExtensions on String {
  String capitalize() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }

  bool get isNullOrEmpty => isEmpty;

  String toTitleCase() {
    if (isEmpty) return this;
    return split(' ').map((str) => str.capitalize()).join(' ');
  }

  String removeWhitespace() => replaceAll(RegExp(r'\s+'), '');

  String reverse() => split('').reversed.join();

  String snakeToCamel() {
    return split(
      '_',
    ).mapIndexed((i, str) => i == 0 ? str : str.capitalize()).join();
  }
}

extension _IterableExtension<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(int, E) f) {
    var index = 0;
    return map((e) => f(index++, e));
  }
}
