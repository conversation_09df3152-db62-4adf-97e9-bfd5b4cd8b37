import 'package:flutter/material.dart';
import 'package:el_tooltip/el_tooltip.dart';
import 'package:bill_book/core/widgets/app_text.dart';

class CustomInfoTooltip extends StatelessWidget {
  final String content;
  final double iconSize;
  final ElTooltipPosition position;
  final Color? iconColor;
  final double distance;
  final Color? tooltipBackgroundColor;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final double borderRadius;
  final bool showModal;

  const CustomInfoTooltip({
    super.key,
    required this.content,
    this.iconSize = 16,
    this.position = ElTooltipPosition.topCenter,
    this.iconColor,
    this.distance = 5,
    this.tooltipBackgroundColor,
    this.textColor = Colors.white,
    this.fontSize = 12,
    this.fontWeight = FontWeight.w500,
    this.borderRadius = 12,
    this.showModal = false,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = tooltipBackgroundColor ?? const Color(0xFF2D3748);
    final effectiveIconColor = iconColor ?? Colors.grey.shade400;

    return ElTooltip(
      position: position,
      timeout: Duration(seconds: 2),
      appearAnimationDuration: Duration(milliseconds: 100),
      disappearAnimationDuration: Duration(milliseconds: 100),
      distance: distance,
      content: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: AppText(
          content,
          color: textColor,
          fontSize: fontSize,
          fontWeight: fontWeight,
        ),
      ),
      showModal: showModal,
      color: backgroundColor,
      child: Icon(
        Icons.info_outline,
        size: iconSize,
        color: effectiveIconColor,
      ),
    );
  }
}
