import 'package:flutter/material.dart';
import '../custom_text_field.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/validators.dart';

class PhoneField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final String countryCode;
  final VoidCallback? onCountryCodeTap;
  final bool isRequired;

  const PhoneField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.countryCode = '+1',
    this.onCountryCodeTap,
    this.isRequired = true,
  });

  String? _validate(String? value) {
    if (validator != null) {
      return validator!(value);
    }
    if (isRequired) {
      return Validators.phone(value);
    }
    if (value != null && value.isNotEmpty) {
      return Validators.phone(value);
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: label,
      hint: hint,
      controller: controller,
      validator: _validate,
      keyboardType: TextInputType.phone,
      onChanged: onChanged,
      prefix: GestureDetector(
        onTap: onCountryCodeTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                countryCode,
                style: const TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.arrow_drop_down, color: AppColors.primary),
            ],
          ),
        ),
      ),
    );
  }
}
