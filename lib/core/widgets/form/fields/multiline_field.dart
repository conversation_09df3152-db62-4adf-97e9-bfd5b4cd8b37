import 'package:flutter/material.dart';
import '../custom_text_field.dart';
import '../../../utils/validators.dart';

class MultilineField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final int? maxLines;
  final int? maxLength;
  final int? minLength;
  final bool isRequired;

  const MultilineField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.maxLines = 5,
    this.maxLength,
    this.minLength,
    this.isRequired = true,
  });

  String? _validate(String? value) {
    if (validator != null) {
      return validator!(value);
    }
    if (isRequired) {
      final requiredError = Validators.required(value);
      if (requiredError != null) return requiredError;
    }
    if (value != null && value.isNotEmpty) {
      if (minLength != null) {
        final minLengthError = Validators.minLength(value, minLength!);
        if (minLengthError != null) return minLengthError;
      }
      if (maxLength != null) {
        final maxLengthError = Validators.maxLength(value, maxLength!);
        if (maxLengthError != null) return maxLengthError;
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: label,
      hint: hint,
      controller: controller,
      validator: _validate,
      onChanged: onChanged,
      maxLines: maxLines,
      maxLength: maxLength,
      keyboardType: TextInputType.multiline,
    );
  }
}
