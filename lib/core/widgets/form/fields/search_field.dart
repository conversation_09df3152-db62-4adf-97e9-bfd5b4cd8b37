import 'package:flutter/material.dart';
import '../custom_text_field.dart';
import '../../../constants/app_colors.dart';

class SearchField extends StatelessWidget {
  final String? hint;
  final TextEditingController? controller;
  final void Function(String)? onChanged;
  final VoidCallback? onClear;

  const SearchField({
    super.key,
    this.hint,
    this.controller,
    this.onChanged,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: '',
      hint: hint ?? 'Search...',
      controller: controller,
      onChanged: onChanged,
      prefix: const Icon(Icons.search, color: AppColors.primary),
      suffix:
          controller?.text.isNotEmpty == true
              ? IconButton(
                icon: const Icon(Icons.clear, color: AppColors.primary),
                onPressed: () {
                  controller?.clear();
                  onClear?.call();
                },
              )
              : null,
    );
  }
}
