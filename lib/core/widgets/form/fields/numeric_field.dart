import 'package:flutter/material.dart';
import '../custom_text_field.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/validators.dart';

class NumericField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final double? min;
  final double? max;
  final double step;
  final bool allowDecimals;
  final bool isRequired;

  const NumericField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.min,
    this.max,
    this.step = 1,
    this.allowDecimals = false,
    this.isRequired = true,
  });

  String? _validate(String? value) {
    if (validator != null) {
      return validator!(value);
    }
    if (isRequired) {
      final requiredError = Validators.required(value);
      if (requiredError != null) return requiredError;
    }
    if (value != null && value.isNotEmpty) {
      final numericError = Validators.numeric(value);
      if (numericError != null) return numericError;

      if (min != null) {
        final minError = Validators.minValue(value, min!);
        if (minError != null) return minError;
      }
      if (max != null) {
        final maxError = Validators.maxValue(value, max!);
        if (maxError != null) return maxError;
      }
    }
    return null;
  }

  void _increment() {
    if (controller != null) {
      final currentValue = double.tryParse(controller!.text) ?? 0;
      final newValue = currentValue + step;
      if (max == null || newValue <= max!) {
        controller!.text =
            allowDecimals ? newValue.toString() : newValue.toInt().toString();
        onChanged?.call(controller!.text);
      }
    }
  }

  void _decrement() {
    if (controller != null) {
      final currentValue = double.tryParse(controller!.text) ?? 0;
      final newValue = currentValue - step;
      if (min == null || newValue >= min!) {
        controller!.text =
            allowDecimals ? newValue.toString() : newValue.toInt().toString();
        onChanged?.call(controller!.text);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      label: label,
      hint: hint,
      controller: controller,
      validator: _validate,
      onChanged: onChanged,
      keyboardType: allowDecimals ? TextInputType.number : TextInputType.number,
      suffix: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.remove, color: AppColors.primary),
            onPressed: _decrement,
          ),
          IconButton(
            icon: const Icon(Icons.add, color: AppColors.primary),
            onPressed: _increment,
          ),
        ],
      ),
    );
  }
}
