import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:bill_book/core/widgets/app_text.dart';

enum ContactPermissionStatus {
  granted,
  denied,
  permanentlyDenied,
  restricted,
  limited,
}

class ContactPermissionService {
  static Future<bool> handleContactsPermission(BuildContext context) async {
    final permission = await FlutterContacts.requestPermission();

    if (permission) {
      return true;
    } else {
      if (context.mounted) {
        await _showPermissionDeniedDialog(context);
      }
      return false;
    }
  }

  /// Checks if we have any contact access (even limited on iOS)
  static Future<bool> hasAnyContactAccess() async {
    try {
      final contacts = await FlutterContacts.getContacts(withProperties: false);
      return contacts.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Requests full contact access (instead of limited access)
  static Future<bool> requestFullContactAccess(BuildContext context) async {
    try {
      // Show a dialog explaining why we need full access
      final shouldRequest = await _showFullAccessRequestDialog(context);

      if (shouldRequest) {
        // Use permission_handler for full system access
        final systemPermission = await Permission.contacts.request();
        print('System permission result in service: $systemPermission');

        // If system permission is permanently denied, guide user to settings
        if (systemPermission.isPermanentlyDenied) {
          if (context.mounted) {
            await _showGoToSettingsDialog(context);
          }
          return false;
        }

        // Also try flutter_contacts as backup
        final flutterPermission = await FlutterContacts.requestPermission();
        print(
          'Flutter contacts permission result in service: $flutterPermission',
        );

        return systemPermission.isGranted || flutterPermission;
      }

      return false;
    } catch (e) {
      print('Error in requestFullContactAccess: $e');
      if (context.mounted) {
        await _showPermissionDeniedDialog(context);
      }
      return false;
    }
  }

  /// Legacy method name for backward compatibility
  static Future<bool> requestAdditionalContacts(BuildContext context) async {
    return requestFullContactAccess(context);
  }

  static Future<void> _showPermissionDeniedDialog(BuildContext context) async {
    HapticFeedback.lightImpact();

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with icon
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(32),
                  ),
                  child: const Icon(
                    Icons.contacts,
                    color: Colors.orange,
                    size: 32,
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                const AppText(
                  'Contacts Permission Required',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Content
                const AppText(
                  'To import contacts as parties, we need access to your contacts. This helps you quickly create customer and supplier entries.',
                  fontSize: 14,
                  color: Colors.black54,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop();
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: AppText(
                          'Cancel',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop();
                          // Try to open app settings
                          await openAppSettings();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF5A67D8),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const AppText(
                          'Open Settings',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<bool> _showFullAccessRequestDialog(BuildContext context) async {
    HapticFeedback.lightImpact();

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with icon
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(32),
                  ),
                  child: const Icon(
                    Icons.contacts,
                    color: Color(0xFF5A67D8),
                    size: 32,
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                const AppText(
                  'Full Contact Access',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Content
                const AppText(
                  'To import all your contacts as parties, we need full access to your contacts. This allows you to select multiple contacts at once and see your complete contact list.',
                  fontSize: 14,
                  color: Colors.black54,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop(false);
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: AppText(
                          'Cancel',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop(true);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF5A67D8),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const AppText(
                          'Grant Full Access',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    return result ?? false;
  }

  static Future<void> _showGoToSettingsDialog(BuildContext context) async {
    HapticFeedback.lightImpact();

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with icon
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(32),
                  ),
                  child: const Icon(
                    Icons.settings,
                    color: Colors.orange,
                    size: 32,
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                const AppText(
                  'Permission Required',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Content
                const AppText(
                  'To access all your contacts, please go to Settings > Privacy & Security > Contacts and enable access for this app.',
                  fontSize: 14,
                  color: Colors.black54,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop();
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: AppText(
                          'Cancel',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop();
                          openAppSettings();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const AppText(
                          'Open Settings',
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
