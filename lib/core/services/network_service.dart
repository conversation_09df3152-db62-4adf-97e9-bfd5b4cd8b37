import 'package:dio/dio.dart';
import '../error/exceptions.dart';
import '../models/api_response.dart';

class NetworkService {
  final Dio _dio;

  NetworkService(this._dio);

  // Base URL - Change this according to your API
  static const String _baseUrl = 'https://api.example.com';

  // Headers
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add your auth token here if needed
    // 'Authorization': 'Bearer $token',
  };

  // GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl$endpoint',
        queryParameters: queryParams,
        options: Options(headers: _headers),
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw UnexpectedException(
        'An unexpected error occurred',
        code: 'NET_004',
      );
    }
  }

  // POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl$endpoint',
        data: body,
        options: Options(headers: _headers),
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw UnexpectedException(
        'An unexpected error occurred',
        code: 'NET_004',
      );
    }
  }

  // PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        '$_baseUrl$endpoint',
        data: body,
        options: Options(headers: _headers),
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw UnexpectedException(
        'An unexpected error occurred',
        code: 'NET_004',
      );
    }
  }

  // DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        '$_baseUrl$endpoint',
        options: Options(headers: _headers),
      );
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      throw UnexpectedException(
        'An unexpected error occurred',
        code: 'NET_004',
      );
    }
  }

  // Handle response
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      final body = response.data is Map<String, dynamic>
          ? response.data as Map<String, dynamic>
          : {};
      switch (response.statusCode) {
        case 200:
        case 201:
          return ApiResponse.success(
            fromJson != null ? fromJson(body['data'] ?? body) : body as T,
            message: body['message'] ?? 'Success',
          );
        case 400:
          throw ValidationException(
            body['message'] ?? 'Invalid request data',
            code: body['code'] ?? 'VAL_001',
          );
        case 401:
          throw AuthenticationException(
            body['message'] ?? 'Authentication required',
            code: body['code'] ?? 'AUTH_001',
          );
        case 403:
          throw AuthorizationException(
            body['message'] ?? 'Access denied',
            code: body['code'] ?? 'AUTH_002',
          );
        case 404:
          throw NotFoundException(
            body['message'] ?? 'Resource not found',
            code: body['code'] ?? 'RES_001',
          );
        case 409:
          throw DuplicateEntryException(
            body['message'] ?? 'Resource already exists',
            code: body['code'] ?? 'DUP_001',
          );
        case 422:
          throw InvalidInputException(
            body['message'] ?? 'Invalid input data',
            code: body['code'] ?? 'VAL_002',
          );
        case 429:
          throw ResourceException(
            body['message'] ?? 'Too many requests',
            code: body['code'] ?? 'RES_002',
          );
        case 500:
        case 502:
        case 503:
        case 504:
          throw ServerException(
            body['message'] ?? 'Server error occurred',
            code: body['code'] ?? 'SRV_001',
          );
        default:
          throw UnexpectedException(
            body['message'] ?? 'An unexpected error occurred',
            code: body['code'] ?? 'UNK_001',
          );
      }
    } on FormatException {
      throw FormatException(
        'Invalid response format:  e.message}',
        code: 'NET_003',
      );
    } catch (e) {
      if (e is AppException) rethrow;
      throw UnexpectedException('Failed to process response', code: 'NET_005');
    }
  }

  void _handleDioError(DioException e) {
    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.sendTimeout ||
        e.type == DioExceptionType.receiveTimeout) {
      throw TimeoutException('Request timed out', code: 'NET_001');
    } else if (e.type == DioExceptionType.badResponse) {
      final response = e.response;
      final body = response?.data is Map<String, dynamic>
          ? response!.data as Map<String, dynamic>
          : {};
      switch (response?.statusCode) {
        case 400:
          throw ValidationException(
            body['message'] ?? 'Invalid request data',
            code: body['code'] ?? 'VAL_001',
          );
        case 401:
          throw AuthenticationException(
            body['message'] ?? 'Authentication required',
            code: body['code'] ?? 'AUTH_001',
          );
        case 403:
          throw AuthorizationException(
            body['message'] ?? 'Access denied',
            code: body['code'] ?? 'AUTH_002',
          );
        case 404:
          throw NotFoundException(
            body['message'] ?? 'Resource not found',
            code: body['code'] ?? 'RES_001',
          );
        case 409:
          throw DuplicateEntryException(
            body['message'] ?? 'Resource already exists',
            code: body['code'] ?? 'DUP_001',
          );
        case 422:
          throw InvalidInputException(
            body['message'] ?? 'Invalid input data',
            code: body['code'] ?? 'VAL_002',
          );
        case 429:
          throw ResourceException(
            body['message'] ?? 'Too many requests',
            code: body['code'] ?? 'RES_002',
          );
        case 500:
        case 502:
        case 503:
        case 504:
          throw ServerException(
            body['message'] ?? 'Server error occurred',
            code: body['code'] ?? 'SRV_001',
          );
        default:
          throw UnexpectedException(
            body['message'] ?? 'An unexpected error occurred',
            code: body['code'] ?? 'UNK_001',
          );
      }
    } else if (e.type == DioExceptionType.connectionError) {
      throw NetworkException('No internet connection', code: 'NET_002');
    } else {
      throw UnexpectedException(
        'An unexpected error occurred',
        code: 'NET_004',
      );
    }
  }
}
