import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:bill_book/features/import_export/models/contact_item.dart';

class ContactService {
  static List<ContactItem>? _cachedContacts;
  static int _lastContactCount = 0;

  /// Gets contacts with support for iOS limited access
  /// [forceRefresh] - Forces a refresh even if contacts are cached
  /// [allowLimitedAccess] - On iOS, allows requesting additional contacts if limited access is granted
  static Future<List<ContactItem>> getContacts({
    bool forceRefresh = false,
    bool allowLimitedAccess = true,
  }) async {
    try {
      // Check if we should refresh the cache
      bool shouldRefresh = forceRefresh || _cachedContacts == null;

      if (!shouldRefresh) {
        // Check if new contacts might be available (iOS limited access scenario)
        final currentContacts = await FlutterContacts.getContacts(
          withProperties: false, // Just get count, not full data
        );

        if (currentContacts.length != _lastContactCount) {
          shouldRefresh = true;
        }
      }

      if (!shouldRefresh) {
        return _cachedContacts!;
      }

      // Check permission status using both methods
      final systemPermission = await Permission.contacts.status;
      print('System permission status: $systemPermission');

      // If system permission is denied, try to request it
      if (systemPermission.isDenied) {
        final requestResult = await Permission.contacts.request();
        print('Permission request result: $requestResult');
      }

      // Also check flutter_contacts permission
      final flutterContactsPermission =
          await FlutterContacts.requestPermission();
      print('FlutterContacts permission: $flutterContactsPermission');

      // Fetch contacts from device
      print('Fetching contacts with properties...');
      final List<Contact> contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withThumbnail: false, // We'll use initials instead
        withPhoto: false,
      );
      print('Fetched ${contacts.length} raw contacts from device');

      // Update contact count for future comparisons
      _lastContactCount = contacts.length;

      // Convert to our ContactItem model
      final List<ContactItem> contactItems = contacts
          .where((contact) => contact.displayName.isNotEmpty)
          .map(
            (contact) => ContactItem(
              id: contact.id,
              name: contact.displayName,
              phoneNumber: contact.phones.isNotEmpty
                  ? contact.phones.first.number
                  : null,
              email: contact.emails.isNotEmpty
                  ? contact.emails.first.address
                  : null,
              company: contact.organizations.isNotEmpty
                  ? contact.organizations.first.company
                  : null,
            ),
          )
          .toList();

      // Sort contacts alphabetically
      contactItems.sort(
        (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()),
      );

      // Cache the contacts
      _cachedContacts = contactItems;

      return contactItems;
    } catch (e) {
      throw ContactServiceException('Failed to load contacts: ${e.toString()}');
    }
  }

  /// Requests full contact access (instead of limited access)
  /// Returns true if full access was granted, false otherwise
  static Future<bool> requestFullContactAccess() async {
    try {
      final previousCount = _lastContactCount;

      // Clear cache first to ensure fresh data
      clearCache();

      // Request full contact permission using system permission handler
      // This is the key - we need to use permission_handler for full access
      print('Requesting full system permission...');

      // Use permission_handler to request full contact access
      final systemPermissionResult = await Permission.contacts.request();
      print('System permission result: $systemPermissionResult');

      // Only proceed if we got full system permission
      if (systemPermissionResult.isGranted) {
        // Wait a moment for iOS to process the permission change
        await Future.delayed(const Duration(milliseconds: 1000));

        // Fetch all contacts to see if we now have full access
        final contacts = await FlutterContacts.getContacts(
          withProperties: false,
        );

        print('Previous count: $previousCount, New count: ${contacts.length}');

        _lastContactCount = contacts.length;

        // Clear cache to ensure fresh data on next load
        clearCache();

        // If permission was granted, consider it successful
        // The contact count might not change if user already had access to all contacts
        if (contacts.length > previousCount) {
          print('Full access granted with new contacts');
          return true;
        } else if (contacts.length == previousCount && previousCount > 0) {
          print('Full access granted but no new contacts');
          return true; // Still successful - user now has full access
        } else if (previousCount == 0) {
          print('Full access granted for first time');
          return true;
        }

        return true; // Permission was granted, so consider it successful
      }

      return false;
    } catch (e) {
      print('Error requesting full contact access: $e');
      clearCache();
      return false;
    }
  }

  /// Legacy method name for backward compatibility
  static Future<bool> requestAdditionalContacts() async {
    return requestFullContactAccess();
  }

  static List<ContactItem> searchContacts(
    List<ContactItem> contacts,
    String query,
  ) {
    if (query.isEmpty) {
      return contacts;
    }

    final String lowerQuery = query.toLowerCase();

    return contacts.where((contact) {
      final String lowerName = contact.name.toLowerCase();
      final String? lowerPhone = contact.phoneNumber?.toLowerCase();
      final String? lowerEmail = contact.email?.toLowerCase();
      final String? lowerCompany = contact.company?.toLowerCase();

      return lowerName.contains(lowerQuery) ||
          (lowerPhone?.contains(lowerQuery) ?? false) ||
          (lowerEmail?.contains(lowerQuery) ?? false) ||
          (lowerCompany?.contains(lowerQuery) ?? false);
    }).toList();
  }

  static void clearCache() {
    _cachedContacts = null;
  }

  static String getContactInitials(String name) {
    if (name.isEmpty) return '';

    final List<String> nameParts = name.trim().split(' ');
    if (nameParts.length == 1) {
      return nameParts[0].substring(0, 1).toUpperCase();
    } else {
      return (nameParts[0].substring(0, 1) + nameParts[1].substring(0, 1))
          .toUpperCase();
    }
  }

  static String formatPhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return '';
    }

    // Remove all non-digit characters
    final String digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length == 10) {
      // Format as (XXX) XXX-XXXX
      return '(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    } else if (digitsOnly.length == 11 && digitsOnly.startsWith('1')) {
      // Format as +1 (XXX) XXX-XXXX
      return '+1 (${digitsOnly.substring(1, 4)}) ${digitsOnly.substring(4, 7)}-${digitsOnly.substring(7)}';
    } else {
      // Return original if it doesn't match common patterns
      return phoneNumber;
    }
  }
}

class ContactServiceException implements Exception {
  final String message;

  ContactServiceException(this.message);

  @override
  String toString() => 'ContactServiceException: $message';
}
