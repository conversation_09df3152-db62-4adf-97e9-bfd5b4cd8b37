abstract class AppException implements Exception {
  final String message;
  final String? code;

  AppException(this.message, {this.code});

  @override
  String toString() => message;
}

class NetworkException extends AppException {
  NetworkException(super.message, {super.code});
}

class ServerException extends AppException {
  ServerException(super.message, {super.code});
}

class ValidationException extends AppException {
  ValidationException(super.message, {super.code});
}

class UnexpectedException extends AppException {
  UnexpectedException(super.message, {super.code});
}

class CacheException extends AppException {
  CacheException(super.message, {super.code});
}

class AuthenticationException extends AppException {
  AuthenticationException(super.message, {super.code});
}

class AuthorizationException extends AppException {
  AuthorizationException(super.message, {super.code});
}

class TimeoutException extends AppException {
  TimeoutException(super.message, {super.code});
}

class NotFoundException extends AppException {
  NotFoundException(super.message, {super.code});
}

class DuplicateEntryException extends AppException {
  DuplicateEntryException(super.message, {super.code});
}

class InvalidInputException extends AppException {
  InvalidInputException(super.message, {super.code});
}

class DatabaseException extends AppException {
  DatabaseException(super.message, {super.code});
}

class FileOperationException extends AppException {
  FileOperationException(super.message, {super.code});
}

class PaymentException extends AppException {
  PaymentException(super.message, {super.code});
}

class LocationException extends AppException {
  LocationException(super.message, {super.code});
}

class PermissionException extends AppException {
  PermissionException(super.message, {super.code});
}

class DeviceException extends AppException {
  DeviceException(super.message, {super.code});
}

class ConfigurationException extends AppException {
  ConfigurationException(super.message, {super.code});
}

class DependencyException extends AppException {
  DependencyException(super.message, {super.code});
}

class StateException extends AppException {
  StateException(super.message, {super.code});
}

class FormatException extends AppException {
  FormatException(super.message, {super.code});
}

class ResourceException extends AppException {
  ResourceException(super.message, {super.code});
}
