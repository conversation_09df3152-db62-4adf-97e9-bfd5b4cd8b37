import 'package:flutter/material.dart';

class CircularRevealClipper extends CustomClipper<Path> {
  final double revealPercent;
  final bool isReversed;

  CircularRevealClipper({
    required this.revealPercent,
    required this.isReversed,
  });

  @override
  Path getClip(Size size) {
    final path = Path();

    final Offset center;
    final double currentRadius;

    center = Offset(size.width * 0.85, size.height * 0.15); // Top-right
    final double maxRadius = _calculateMaxRadius(size, center);
    currentRadius = maxRadius * revealPercent;

    // Create circular path
    path.addOval(Rect.fromCircle(center: center, radius: currentRadius));

    return path;
  }

  double _calculateMaxRadius(Size size, Offset center) {
    // Calculate distance to all four corners and return the maximum
    final double topLeft = (center - const Offset(0, 0)).distance;
    final double topRight = (center - Offset(size.width, 0)).distance;
    final double bottomLeft = (center - Offset(0, size.height)).distance;
    final double bottomRight =
        (center - Offset(size.width, size.height)).distance;

    return [
      topLeft,
      topRight,
      bottomLeft,
      bottomRight,
    ].reduce((a, b) => a > b ? a : b);
  }

  @override
  bool shouldReclip(CircularRevealClipper oldClipper) {
    return oldClipper.revealPercent != revealPercent ||
        oldClipper.isReversed != isReversed;
  }
}
