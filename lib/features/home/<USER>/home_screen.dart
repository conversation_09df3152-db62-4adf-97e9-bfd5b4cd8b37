import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/home_bloc.dart';
import '../bloc/home_event.dart';
import '../bloc/home_state.dart';
import '../tabs/dashboard_tab.dart';
import '../tabs/parties_tab.dart';
import '../tabs/items_tab.dart';
import '../tabs/for_you_tab.dart';
import '../tabs/more_tab.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String? _partiesFilter;

  void _navigateToPartiesWithFilter(String? filter, BuildContext blocContext) {
    setState(() {
      _partiesFilter = filter;
    });
    // Use the provided context that has access to the BlocProvider
    blocContext.read<HomeBloc>().add(HomeTabChanged(1));
  }

  void _onTabChanged(int index, BuildContext blocContext) {
    // Clear parties filter when navigating away from parties tab
    if (index != 1 && _partiesFilter != null) {
      setState(() {
        _partiesFilter = null;
      });
    }
    blocContext.read<HomeBloc>().add(HomeTabChanged(index));
  }

  List<Widget> _tabs(
    BuildContext blocContext,
    void Function(int) onTabChange,
  ) => [
    DashboardTab(
      onGoToParties: (String? filter) =>
          _navigateToPartiesWithFilter(filter, blocContext),
    ),
    PartiesTab(initialFilter: _partiesFilter),
    const ItemsTab(),
    const ForYouTab(),
    const MoreTab(),
  ];

  static final List<BottomNavigationBarItem> _bottomNavItems = [
    BottomNavigationBarItem(
      icon: Icon(Icons.dashboard, size: 28),
      activeIcon: Icon(
        Icons.dashboard_customize,
        color: Colors.deepPurple,
        size: 32,
      ),
      label: 'Dashboard',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.people, size: 28),
      activeIcon: Icon(Icons.people_alt, color: Colors.deepPurple, size: 32),
      label: 'Parties',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.inventory, size: 28),
      activeIcon: Icon(Icons.inventory_2, color: Colors.deepPurple, size: 32),
      label: 'Items',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.recommend, size: 28),
      activeIcon: Icon(Icons.thumb_up, color: Colors.deepPurple, size: 32),
      label: 'For you',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.menu, size: 28),
      activeIcon: Icon(Icons.menu_open, color: Colors.deepPurple, size: 32),
      label: 'More',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => HomeBloc(),
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (blocContext, state) {
          return Scaffold(
            extendBody: true,
            body: AnimatedSwitcher(
              duration: const Duration(milliseconds: 350),
              child: _tabs(
                blocContext,
                (index) => _onTabChanged(index, blocContext),
              )[state.selectedIndex],
            ),
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 16,
                    offset: const Offset(0, -2),
                  ),
                ],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(18),
                  topRight: Radius.circular(18),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.only(
                  bottom: 8,
                  left: 8,
                  right: 8,
                  top: 2,
                ),
                child: BottomNavigationBar(
                  currentIndex: state.selectedIndex,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  selectedItemColor: Colors.deepPurple,
                  unselectedItemColor: Colors.grey[500],
                  showUnselectedLabels: true,
                  items: _bottomNavItems,
                  onTap: (index) => _onTabChanged(index, blocContext),
                  type: BottomNavigationBarType.fixed,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
