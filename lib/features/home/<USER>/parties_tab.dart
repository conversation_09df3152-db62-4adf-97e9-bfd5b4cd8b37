import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/router/app_routes.dart';

class PartiesTab extends StatefulWidget {
  final String? initialFilter;

  const PartiesTab({super.key, this.initialFilter});

  @override
  State<PartiesTab> createState() => _PartiesTabState();
}

class _PartiesTabState extends State<PartiesTab> with TickerProviderStateMixin {
  String _selectedFilter = '';

  // Filter state variables
  String _sortBy = '';
  final Set<String> _selectedCustomerTypes = {};

  // Settings state variables
  bool _partyBirthdayReminders = false;

  // Search state variables
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Set initial filter if provided
    if (widget.initialFilter != null) {
      _selectedFilter = widget.initialFilter!;
    }
  }

  // Sample party data
  final List<Map<String, dynamic>> _parties = [
    {'name': 'Harsh', 'type': 'Supplier', 'amount': 10000, 'isPositive': false},
    {'name': 'Cash Sale', 'type': 'Customer', 'amount': 10, 'isPositive': true},
    {
      'name': 'John Doe',
      'type': 'Customer',
      'amount': 5000,
      'isPositive': true,
    },
    {
      'name': 'ABC Suppliers',
      'type': 'Supplier',
      'amount': 15000,
      'isPositive': false,
    },
    {
      'name': 'Retail Store',
      'type': 'Customer',
      'amount': 2500,
      'isPositive': true,
    },
    {
      'name': 'XYZ Distributors',
      'type': 'Supplier',
      'amount': 8000,
      'isPositive': false,
    },
  ];

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              children: [
                // Header with animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return SlideTransition(
                          position: animation.drive(
                            Tween(
                              begin: const Offset(0.0, -0.5),
                              end: Offset.zero,
                            ).chain(CurveTween(curve: Curves.easeOutCubic)),
                          ),
                          child: FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        );
                      },
                  child: _isSearchMode ? _buildSearchHeader() : _buildHeader(),
                ),

                // Filter Tabs (hidden in search mode)
                _buildFilterTabs(),

                // Party List
                Expanded(child: _buildPartyList()),
              ],
            ),
          ),

          // Floating Action Buttons
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: _buildFloatingActionButtons(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 350),
      child: SlideAnimation(
        verticalOffset: -20.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            key: const ValueKey('normal_header'),
            padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
            color: Colors.white,
            child: Row(
              children: [
                const Expanded(
                  child: AppText(
                    'Parties',
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        _toggleSearchMode();
                      },
                      icon: const Icon(
                        Icons.search,
                        color: Colors.grey,
                        size: 24,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // Handle share
                      },
                      icon: const Icon(
                        Icons.share,
                        color: Colors.grey,
                        size: 24,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        _showSettingsBottomSheet();
                      },
                      icon: const Icon(
                        Icons.settings,
                        color: Colors.grey,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return AnimationConfiguration.staggeredList(
      position: 1,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        horizontalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            color: Colors.white,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                spacing: 10,
                children: [
                  _buildFilterChip('To Pay'),
                  _buildFilterChip('To Collect'),
                  _buildCategoryDropdown(),
                  _buildFilterButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    final bool isSelected = _selectedFilter == label;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          if (isSelected) {
            // Clear the filter if already selected
            _selectedFilter = '';
          } else {
            _selectedFilter = label;
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 12 : 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              label,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : Colors.grey[700],
            ),
            if (isSelected) ...[
              const SizedBox(width: 6),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _selectedFilter = '';
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(2),
                  child: const Icon(Icons.close, size: 12, color: Colors.white),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppText(
            'Category',
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
          const SizedBox(width: 4),
          Icon(Icons.keyboard_arrow_down, size: 16, color: Colors.grey[700]),
        ],
      ),
    );
  }

  Widget _buildFilterButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showFilterBottomSheet();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              'Filter By',
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
            const SizedBox(width: 4),
            Icon(Icons.tune, size: 16, color: Colors.grey[700]),
          ],
        ),
      ),
    );
  }

  Widget _buildPartyList() {
    final filteredParties = _parties.where((party) {
      // First apply search filter
      bool matchesSearch = true;
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final name = party['name'].toString().toLowerCase();
        final type = party['type'].toString().toLowerCase();
        matchesSearch = name.contains(query) || type.contains(query);
      }

      // Then apply other filters
      bool matchesFilter = true;
      if (_selectedFilter == 'To Pay') {
        matchesFilter = !party['isPositive'] && party['amount'] > 0;
      } else if (_selectedFilter == 'To Collect') {
        matchesFilter = party['isPositive'] && party['amount'] > 0;
      }

      return matchesSearch && matchesFilter;
    }).toList();

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredParties.length,
        itemBuilder: (context, index) {
          final party = filteredParties[index];
          return AnimationConfiguration.staggeredList(
            position: index + 2,
            duration: const Duration(milliseconds: 300),
            delay: const Duration(milliseconds: 100),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildPartyCard(party, index),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPartyCard(Map<String, dynamic> party, int index) {
    final bool isPositive = party['isPositive'];
    final double amount = party['amount'].toDouble();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  party['name'],
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                const SizedBox(height: 4),
                AppText(party['type'], fontSize: 12, color: Colors.grey[600]),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppText(
                    '₹ ${amount.toStringAsFixed(0)}',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isPositive ? Colors.green : Colors.red,
                  ),
                  if (!isPositive && amount > 0) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.red,
                      size: 16,
                    ),
                  ],
                ],
              ),
              if (!isPositive && amount > 0) ...[
                const SizedBox(height: 8),
                _buildSendReminderButton(),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSendReminderButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle send reminder
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              'Send Reminder',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.green[700],
            ),
            const SizedBox(width: 4),
            Icon(Icons.message, size: 12, color: Colors.green[700]),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return SafeArea(
      child: Row(
        children: [
          // Create Party Button
          Expanded(
            child: AnimationConfiguration.staggeredList(
              position: 0,
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 200),
              child: SlideAnimation(
                verticalOffset: 50.0,
                curve: Curves.easeOutBack,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: ScaleAnimation(
                    scale: 0.8,
                    curve: Curves.easeOutBack,
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: const Color(0xFF5A67D8),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: TextButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.pushNamed(context, CreatePartyRoute.name);
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.add, color: Colors.white, size: 18),
                            SizedBox(width: 8),
                            AppText(
                              'Create Party',
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Import/Export Button
          AnimationConfiguration.staggeredList(
            position: 1,
            duration: const Duration(milliseconds: 400),
            delay: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 50.0,
              curve: Curves.easeOutBack,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: ScaleAnimation(
                  scale: 0.8,
                  curve: Curves.easeOutBack,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4A5568),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        Navigator.pushNamed(context, ImportExportRoute.name);
                      },
                      icon: const Icon(
                        Icons.import_export,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFilterBottomSheet(),
    );
  }

  Widget _buildFilterBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.65,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildBottomSheetHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: AnimationLimiter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 300),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 30.0,
                            child: FadeInAnimation(child: widget),
                          ),
                          children: [
                            _buildSortBySection(setModalState),
                            const SizedBox(height: 24),
                            _buildFilterBySection(setModalState),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Action Buttons
                _buildBottomSheetActions(setModalState),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                const AppText(
                  'Sort & Filter',
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B6B),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const AppText(
                    'NEW',
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 20, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortBySection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const AppText(
              'Sort By',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            const Spacer(),
            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setModalState(() {
                  _sortBy = '';
                });
              },
              child: const AppText(
                'CLEAR',
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildSortOption('Party name - A to Z', 'name_asc', setModalState),
        const SizedBox(height: 12),
        _buildSortOption('Party name - Z to A', 'name_desc', setModalState),
        const SizedBox(height: 12),
        _buildSortOption('Amount - High to Low', 'amount_desc', setModalState),
        const SizedBox(height: 12),
        _buildSortOption('Amount - Low to High', 'amount_asc', setModalState),
      ],
    );
  }

  Widget _buildSortOption(
    String title,
    String value,
    StateSetter setModalState,
  ) {
    final bool isSelected = _sortBy == value;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setModalState(() {
          _sortBy = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            _buildSortIcon(value),
            const SizedBox(width: 12),
            Expanded(
              child: AppText(title, fontSize: 14, color: Colors.black87),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected
                    ? const Color(0xFF5A67D8)
                    : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, size: 12, color: Colors.white)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortIcon(String value) {
    IconData iconData;
    Color iconColor = const Color(0xFF5A67D8);

    switch (value) {
      case 'name_asc':
        iconData = Icons.sort_by_alpha;
        break;
      case 'name_desc':
        iconData = Icons.sort_by_alpha;
        break;
      case 'amount_desc':
        iconData = Icons.trending_down;
        break;
      case 'amount_asc':
        iconData = Icons.trending_up;
        break;
      default:
        iconData = Icons.sort;
    }

    return Icon(iconData, size: 20, color: iconColor);
  }

  Widget _buildFilterBySection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Filter By',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildFilterChipOption('New Customers', setModalState),
            _buildFilterChipOption('Old Customers', setModalState),
            _buildFilterChipOption('Active Customer', setModalState),
            _buildFilterChipOption('Inactive Customer', setModalState),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChipOption(String label, StateSetter setModalState) {
    final bool isSelected = _selectedCustomerTypes.contains(label);

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setModalState(() {
          if (isSelected) {
            _selectedCustomerTypes.remove(label);
          } else {
            _selectedCustomerTypes.add(label);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
          ),
        ),
        child: AppText(
          label,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildBottomSheetActions(StateSetter setModalState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              // Apply filters and close bottom sheet
              setState(() {
                // Apply the filters to the main state
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5A67D8),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const AppText(
              'Apply',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _showSettingsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSettingsBottomSheet(),
    );
  }

  Widget _buildSettingsBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.5,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildSettingsHeader(),

                // Content
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: AnimationLimiter(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: AnimationConfiguration.toStaggeredList(
                        duration: const Duration(milliseconds: 300),
                        childAnimationBuilder: (widget) => SlideAnimation(
                          verticalOffset: 30.0,
                          child: FadeInAnimation(child: widget),
                        ),
                        children: [
                          _buildAddFieldsOption(),
                          const SizedBox(height: 16),
                          _buildBirthdayReminderOption(setModalState),
                          const SizedBox(height: 16),
                          Spacer(),
                          _buildSettingsFooter(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Parties Settings',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.close,
                size: 20,
                color: Color(0xFF5A67D8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddFieldsOption() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle add fields navigation
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AppText(
                  'Add Fields to Party',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                const SizedBox(height: 4),
                AppText(
                  'Add extra fields in party section of invoices.\nEg: Drug Licence No. and other Custom\nFields',
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ],
            ),
            const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildBirthdayReminderOption(StateSetter setModalState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Party Birthday reminders',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          Switch(
            value: _partyBirthdayReminders,
            onChanged: (value) {
              HapticFeedback.lightImpact();
              setModalState(() {
                _partyBirthdayReminders = value;
              });
              setState(() {
                _partyBirthdayReminders = value;
              });
            },
            activeColor: const Color(0xFF5A67D8),
            inactiveThumbColor: Colors.grey[400],
            inactiveTrackColor: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: AppText(
          'These settings will apply to all parties',
          fontSize: 12,
          color: Colors.grey[600],
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // Search functionality methods
  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (_isSearchMode) {
        // Focus the search field when entering search mode
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _searchFocusNode.requestFocus();
        });
      } else {
        // Clear search when exiting search mode
        _searchController.clear();
        _searchQuery = '';
        _searchFocusNode.unfocus();
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
  }

  Widget _buildSearchHeader() {
    return Container(
      key: const ValueKey('search_header'),
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
      color: Colors.white,
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _toggleSearchMode();
            },
            icon: const Icon(Icons.arrow_back, color: Colors.grey, size: 24),
          ),
          // Search field
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: 'Search parties...',
                  hintStyle: TextStyle(color: Colors.grey[600], fontSize: 14),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          onPressed: _clearSearch,
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
