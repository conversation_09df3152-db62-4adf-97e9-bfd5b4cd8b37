import 'package:flutter/material.dart';

import '../../../../core/widgets/app_text.dart';

Widget buildTransactionItem({
  required String name,
  required String description,
  required String amount,
  required String date,
  required String status,
  required Color statusColor,
}) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(name, fontSize: 16, fontWeight: FontWeight.w600),
              const SizedBox(height: 4),
              AppText(description, fontSize: 14, color: Colors.grey[600]),
              const SizedBox(height: 4),
              AppText(date, fontSize: 12, color: Colors.grey[500]),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            AppText(amount, fontSize: 16, fontWeight: FontWeight.w600),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: AppText(
                status,
                fontSize: 12,
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
