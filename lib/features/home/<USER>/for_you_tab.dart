import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';

class ForYouTab extends StatefulWidget {
  const ForYouTab({super.key});

  @override
  State<ForYouTab> createState() => _ForYouTabState();
}

class _ForYouTabState extends State<ForYouTab> with TickerProviderStateMixin {
  bool _showAllAccounting = false;
  late AnimationController _arrowAnimationController;
  late Animation<double> _arrowRotationAnimation;

  @override
  void initState() {
    super.initState();
    _arrowAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _arrowRotationAnimation =
        Tween<double>(
          begin: 0.0,
          end: 0.5, // 0.5 = 180 degrees rotation
        ).animate(
          CurvedAnimation(
            parent: _arrowAnimationController,
            curve: Curves.easeInOut,
          ),
        );
  }

  @override
  void dispose() {
    _arrowAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 25),
          child: AnimationLimiter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 400),
                delay: const Duration(milliseconds: 100),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 30.0,
                  curve: Curves.easeOutCubic,
                  child: FadeInAnimation(
                    curve: Curves.easeOutCubic,
                    child: widget,
                  ),
                ),
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildRecommendedSection(),
                  const SizedBox(height: 24),
                  _buildBusinessCardPromo(),
                  const SizedBox(height: 24),
                  _buildMarketingSalesSection(),
                  const SizedBox(height: 24),
                  _buildAccountingSection(),
                  const SizedBox(height: 24),
                  _buildBusinessEfficiencySection(),
                  const SizedBox(height: 16),
                  _buildDisclaimerText(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF5A67D8), Color(0xFF7C4DFF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const AppText(
                'For You',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const AppText(
            'Features designed specially for your business',
            fontSize: 14,
            color: Colors.white70,
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const AppText(
            'Recommended for you',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildRecommendedItem(
                  'e-Invoice',
                  Icons.description,
                  const Color(0xFF5A67D8),
                  hasNewBadge: true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildRecommendedItem(
                  'Staff Attendance & Payroll',
                  Icons.people,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildRecommendedItem(
                  'Desktop Software',
                  Icons.computer,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedItem(
    String title,
    IconData icon,
    Color color, {
    bool hasNewBadge = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle tap
      },
      child: Container(
        height: 120, // Fixed height for consistent appearance
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Icon section with fixed space
            Stack(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                if (hasNewBadge)
                  Positioned(
                    top: -4,
                    right: -4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const AppText(
                        'NEW',
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            // Text section with proper constraints
            SizedBox(
              height:
                  32, // Fixed height for text area (2 lines * 16px line height)
              child: Center(
                child: AppText(
                  title,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessCardPromo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF2C3E50), Color(0xFFE74C3C)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AppText(
                  'Create Business Cards and send personal greetings!',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const AppText(
                    'Download mySandesh',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 80,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.card_membership,
              color: Colors.white,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarketingSalesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Marketing & Sales',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildFeatureItem(
                'WhatsApp Marketing',
                Icons.chat,
                const Color(0xFF25D366),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildFeatureItem(
                'Reward Points',
                Icons.stars,
                const Color(0xFF5A67D8),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildFeatureItem(
                'Notes and Appointments',
                Icons.event_note,
                const Color(0xFF5A67D8),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildFeatureItem(
                'Online Store',
                Icons.store,
                const Color(0xFF5A67D8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle tap
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            AppText(
              title,
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountingSection() {
    final accountingItems = [
      {
        'title': 'GST Filing',
        'icon': Icons.description,
        'color': const Color(0xFF5A67D8),
      },
      {
        'title': 'Balance Sheet',
        'icon': Icons.balance,
        'color': const Color(0xFF5A67D8),
      },
      {
        'title': 'Automated Bills',
        'icon': Icons.receipt_long,
        'color': const Color(0xFF5A67D8),
      },
      {
        'title': 'CA Reports Sharing',
        'icon': Icons.share,
        'color': const Color(0xFF5A67D8),
        'hasNewBadge': true,
      },
      {
        'title': 'Reports',
        'icon': Icons.assessment,
        'color': const Color(0xFF5A67D8),
      },
      {
        'title': 'Data Export to Tally',
        'icon': Icons.upload_file,
        'color': const Color(0xFF5A67D8),
      },
    ];

    final visibleItems = _showAllAccounting
        ? accountingItems
        : accountingItems.take(4).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Accounting',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 16),
        AnimatedSize(
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeInOut,
          child: AnimationLimiter(
            key: ValueKey(
              _showAllAccounting,
            ), // Force re-animation on state change
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: visibleItems.length,
              itemBuilder: (context, index) {
                final item = visibleItems[index];
                return AnimationConfiguration.staggeredGrid(
                  position: index,
                  duration: const Duration(milliseconds: 300),
                  delay: const Duration(milliseconds: 50),
                  columnCount: 4,
                  child: SlideAnimation(
                    verticalOffset: 30.0,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildAccountingItem(
                        item['title'] as String,
                        item['icon'] as IconData,
                        item['color'] as Color,
                        hasNewBadge: item['hasNewBadge'] as bool? ?? false,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        if (accountingItems.length > 4) ...[
          const SizedBox(height: 16),
          Center(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();

                setState(() {
                  _showAllAccounting = !_showAllAccounting;
                });

                // Animate arrow rotation after state change
                if (_showAllAccounting) {
                  _arrowAnimationController.forward();
                } else {
                  _arrowAnimationController.reverse();
                }
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppText(
                    _showAllAccounting ? 'View Less' : 'View More',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF5A67D8),
                  ),
                  const SizedBox(width: 4),
                  AnimatedBuilder(
                    animation: _arrowRotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle:
                            _arrowRotationAnimation.value *
                            3.14159, // Convert to radians
                        child: const Icon(
                          Icons.keyboard_arrow_down,
                          color: Color(0xFF5A67D8),
                          size: 20,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAccountingItem(
    String title,
    IconData icon,
    Color color, {
    bool hasNewBadge = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle tap
      },
      child: Container(
        padding: const EdgeInsets.all(
          10,
        ), // Reduced padding to prevent overflow
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            // Icon section
            Flexible(
              flex: 2,
              child: Stack(
                children: [
                  Container(
                    width: 36, // Slightly smaller icon container
                    height: 36,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Icon(icon, color: color, size: 18), // Smaller icon
                  ),
                  if (hasNewBadge)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const AppText(
                          'NEW',
                          fontSize: 6,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Text section with flexible space
            Flexible(
              flex: 2,
              child: Center(
                child: AppText(
                  title,
                  fontSize: 10, // Slightly smaller font
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessEfficiencySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Business Efficiency',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildFeatureItem(
                'Smart Calculator',
                Icons.calculate,
                const Color(0xFF5A67D8),
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(child: SizedBox()), // Empty space for alignment
            const SizedBox(width: 16),
            const Expanded(child: SizedBox()), // Empty space for alignment
            const SizedBox(width: 16),
            const Expanded(child: SizedBox()), // Empty space for alignment
          ],
        ),
      ],
    );
  }

  Widget _buildDisclaimerText() {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
      child: const AppText(
        'The use of CA logo does not imply any endorsement, affiliation, or association with the ICAI. The logo is the intellectual property of ICAI, and all rights to the logo remain with them',
        fontSize: 11,
        color: Colors.grey,
        textAlign: TextAlign.center,
        maxLines: 4,
      ),
    );
  }
}
