import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/router/app_routes.dart';
import '../../../core/widgets/app_text.dart';
import '../models/transaction_option.dart';
import '../widgets/custom_circular_reveal_clipper.dart';
import '../widgets/dashboard/dashboard_header.dart';
import '../widgets/dashboard/dashboard_summary_card.dart';
import '../widgets/dashboard/dashboard_transaction_item.dart';

class DashboardTab extends StatefulWidget {
  final void Function(String? filter)? onGoToParties;
  const DashboardTab({super.key, this.onGoToParties});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab>
    with TickerProviderStateMixin {
  String _selectedDateFilter = 'LAST 365 DAYS';
  DateTimeRange? _customDateRange;

  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _circularRevealAnimation;

  bool _showFloatingHeader = false;
  String _selectedFilter = 'All';

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _circularRevealAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    const threshold = 200.0;

    if (_scrollController.offset > threshold && !_showFloatingHeader) {
      setState(() {
        _showFloatingHeader = true;
      });
      _animationController.forward();
    } else if (_scrollController.offset <= threshold && _showFloatingHeader) {
      _animationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _showFloatingHeader = false;
          });
        }
      });
    }
  }

  // Dummy transaction data
  final List<Map<String, dynamic>> _dummyTransactions = const [
    {
      'name': 'Harsh',
      'description': 'Purchase Return #1',
      'amount': '₹ 50',
      'date': '25 Jun',
      'status': 'Credited',
      'statusColor': Colors.blue,
    },
    {
      'name': 'Harsh',
      'description': 'Purchase #1',
      'amount': '₹ 50',
      'date': '25 Jun',
      'status': 'Paid',
      'statusColor': Colors.green,
    },
    {
      'name': 'Harsh',
      'description': 'Received Payment #1',
      'amount': '₹ 15',
      'date': '25 Jun',
      'status': 'Received',
      'statusColor': Colors.green,
    },
    {
      'name': 'Rajesh',
      'description': 'Sale Invoice #5',
      'amount': '₹ 1,200',
      'date': '24 Jun',
      'status': 'Pending',
      'statusColor': Colors.orange,
    },
    {
      'name': 'Priya',
      'description': 'Purchase #3',
      'amount': '₹ 800',
      'date': '23 Jun',
      'status': 'Paid',
      'statusColor': Colors.green,
    },
    {
      'name': 'Amit',
      'description': 'Received Payment #2',
      'amount': '₹ 300',
      'date': '22 Jun',
      'status': 'Received',
      'statusColor': Colors.green,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 400),
                    delay: const Duration(milliseconds: 100),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 30.0,
                      curve: Curves.easeOutCubic,
                      child: FadeInAnimation(
                        curve: Curves.easeOutCubic,
                        child: widget,
                      ),
                    ),
                    children: [
                      // Header
                      AnimationConfiguration.staggeredList(
                        position: 0,
                        duration: const Duration(milliseconds: 350),
                        child: SlideAnimation(
                          verticalOffset: -20.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: buildHeader(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Summary Cards Row 1
                      AnimationConfiguration.staggeredList(
                        position: 1,
                        duration: const Duration(milliseconds: 400),
                        child: SlideAnimation(
                          horizontalOffset: -30.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: Row(
                              children: [
                                Expanded(
                                  child: buildSummaryCard(
                                    title: '₹ 10,000',
                                    subtitle: 'To Collect',
                                    color: const Color(0xFFE8F5E8),
                                    textColor: Colors.green,
                                    icon: Icons.arrow_downward,
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      widget.onGoToParties?.call('To Collect');
                                    },
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: buildSummaryCard(
                                    title: '₹ 0',
                                    subtitle: 'To Pay',
                                    color: const Color(0xFFFFF0F0),
                                    textColor: Colors.red,
                                    icon: Icons.arrow_upward,
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      widget.onGoToParties?.call('To Pay');
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Summary Cards Row 2
                      AnimationConfiguration.staggeredList(
                        position: 2,
                        duration: const Duration(milliseconds: 400),
                        child: SlideAnimation(
                          horizontalOffset: 30.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: Row(
                              children: [
                                Expanded(
                                  child: buildSummaryCard(
                                    title: 'Stock Value',
                                    subtitle: 'Value of Items',
                                    color: Colors.white,
                                    textColor: Colors.black87,
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        StockSummaryRoute.name,
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: buildSummaryCard(
                                    title: '₹ 0',
                                    subtitle: 'This week\'s sale',
                                    color: Colors.white,
                                    textColor: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Bottom Cards Row
                      AnimationConfiguration.staggeredList(
                        position: 3,
                        duration: const Duration(milliseconds: 400),
                        child: SlideAnimation(
                          horizontalOffset: -30.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: Row(
                              children: [
                                Expanded(
                                  child: buildSummaryCard(
                                    title: 'Total Balance',
                                    subtitle: 'Cash + Bank Balance',
                                    color: Colors.white,
                                    textColor: Colors.black87,
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        CashBankRoute.name,
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: buildSummaryCard(
                                    title: 'Reports',
                                    subtitle: 'Sales, Party, GST...',
                                    color: Colors.white,
                                    textColor: Colors.black87,
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        ReportsRoute.name,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // View unlimited reports
                      AnimationConfiguration.staggeredList(
                        position: 4,
                        duration: const Duration(milliseconds: 350),
                        child: ScaleAnimation(
                          scale: 0.9,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: _buildUnlimitedReportsCard(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Transactions Section
                      AnimationConfiguration.staggeredList(
                        position: 5,
                        duration: const Duration(milliseconds: 400),
                        child: SlideAnimation(
                          verticalOffset: 40.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: _buildTransactionsSection(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Floating Header
          if (_showFloatingHeader)
            AnimatedBuilder(
              animation: _circularRevealAnimation,
              builder: (context, child) {
                return ClipPath(
                  clipper: CircularRevealClipper(
                    revealPercent: _circularRevealAnimation.value,
                    isReversed:
                        _animationController.status == AnimationStatus.reverse,
                  ),
                  child: _buildFloatingHeader(),
                );
              },
            ),
          // Floating Action Buttons
          Positioned(
            bottom: 10,
            left: 0,
            right: 0,
            child: _buildFloatingActionButtons(),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          spacing: 10,
          children: [
            // Received Payment Button
            Expanded(
              child: AnimationConfiguration.staggeredList(
                position: 0,
                duration: const Duration(milliseconds: 400),
                delay: const Duration(milliseconds: 200),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  curve: Curves.easeOutBack,
                  child: FadeInAnimation(
                    curve: Curves.easeOutCubic,
                    child: ScaleAnimation(
                      scale: 0.8,
                      curve: Curves.easeOutBack,
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFF4A5568),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextButton(
                          onPressed: () {
                            // Handle received payment action
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: const AppText(
                            'Received Payment',
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Plus Button (Center) - Special animation with bounce
            AnimationConfiguration.staggeredList(
              position: 1,
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 300),
              child: SlideAnimation(
                verticalOffset: 60.0,
                curve: Curves.easeOutBack,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: ScaleAnimation(
                    scale: 0.6,
                    curve: Curves.elasticOut,
                    child: Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: const Color(0xFF4CAF50),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: () {
                          _showTransactionBottomSheet(context);
                        },
                        icon: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Bill/Invoice Button
            Expanded(
              child: AnimationConfiguration.staggeredList(
                position: 2,
                duration: const Duration(milliseconds: 400),
                delay: const Duration(milliseconds: 400),
                child: SlideAnimation(
                  verticalOffset: 50.0,
                  curve: Curves.easeOutBack,
                  child: FadeInAnimation(
                    curve: Curves.easeOutCubic,
                    child: ScaleAnimation(
                      scale: 0.8,
                      curve: Curves.easeOutBack,
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFF5A67D8),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextButton(
                          onPressed: () {
                            // Handle bill/invoice action
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.add, color: Colors.white, size: 16),
                              SizedBox(width: 4),
                              AppText(
                                'Bill / Invoice',
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingHeader() {
    return SafeArea(
      child: Container(
        // margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Search Bar
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.search, color: Colors.grey[600], size: 20),
                        const SizedBox(width: 8),
                        const AppText(
                          'Search party or invoice no.',
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4A5568),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.sort, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      const AppText(
                        'SORT',
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Filter Chips
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('All'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Sales'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Received Payment'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Sales Return'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Credit Note'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Quotation'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Delivery Challan'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Proforma Invoice'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Purchase'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Payment Out'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Purchase Return'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Debit Note'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Purchase Order'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Expense'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Date Filter
            Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.grey[600], size: 16),
                const SizedBox(width: 8),
                AppText(
                  _selectedDateFilter,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                const SizedBox(width: 8),
                AppText(
                  '08 Jul 2024 - 08 Jul 2025',
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                const Spacer(),
                GestureDetector(
                  onTap: _showDateFilterBottomSheet,
                  child: const AppText(
                    'CHANGE',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTransactionBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      showDragHandle: false,
      backgroundColor: Colors.transparent,

      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Close button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 18,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                // padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    // Sales Transactions Section
                    _buildTransactionSection('Sales Transactions', [
                      TransactionOption(
                        'Bill / Invoice',
                        Icons.receipt_long,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Received Payment',
                        Icons.arrow_downward,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Sales Return',
                        Icons.assignment_return,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Credit Note',
                        Icons.note_add,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Quotation/ Estimate',
                        Icons.description,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Delivery Challan',
                        Icons.local_shipping,
                        Colors.green,
                      ),
                      TransactionOption(
                        'Proforma Invoice',
                        Icons.assignment,
                        Colors.green,
                        hasCrown: true,
                      ),
                      TransactionOption(
                        'Automated Bill',
                        Icons.auto_awesome,
                        Colors.green,
                        hasCrown: true,
                      ),
                      TransactionOption('Counter', Icons.store, Colors.orange),
                    ]),
                    Divider(), SizedBox(height: 10),

                    // Purchase Transactions Section
                    _buildTransactionSection('Purchase Transactions', [
                      TransactionOption(
                        'Purchase',
                        Icons.shopping_cart,
                        Colors.blue,
                      ),
                      TransactionOption(
                        'Payment Out',
                        Icons.arrow_upward,
                        Colors.blue,
                      ),
                      TransactionOption(
                        'Purchase Return',
                        Icons.assignment_return,
                        Colors.blue,
                      ),
                      TransactionOption(
                        'Debit Note',
                        Icons.note_add,
                        Colors.blue,
                      ),
                      TransactionOption(
                        'Purchase Order',
                        Icons.shopping_bag,
                        Colors.blue,
                      ),
                      TransactionOption(
                        'Scan & Record Bills',
                        Icons.document_scanner,
                        Colors.blue,
                        isNew: true,
                      ),
                    ]),
                    Divider(),
                    SizedBox(height: 10),

                    // Other Transactions Section
                    _buildTransactionSection('Other Transactions', [
                      TransactionOption(
                        'Expense',
                        Icons.money_off,
                        Colors.red,
                        isNew: true,
                      ),
                    ]),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionSection(
    String title,
    List<TransactionOption> options,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: AppText(
            title,
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 10),
        _buildTransactionGrid(options),
      ],
    );
  }

  Widget _buildTransactionGrid(List<TransactionOption> options) {
    return Column(
      children: [
        // Manually define each row
        _buildTransactionRow(options, 0, 4), // Row 1: items 0-3
        if (options.length > 4)
          _buildTransactionRow(options, 4, 4), // Row 2: items 4-7
        if (options.length > 8)
          _buildTransactionRow(options, 8, 4), // Row 3: items 8-11
        if (options.length > 12)
          _buildTransactionRow(options, 12, 4), // Row 4: items 12-15
      ],
    );
  }

  Widget _buildTransactionRow(
    List<TransactionOption> options,
    int startIndex,
    int maxItems,
  ) {
    List<TransactionOption> rowItems = [];
    for (
      int i = startIndex;
      i < startIndex + maxItems && i < options.length;
      i++
    ) {
      rowItems.add(options[i]);
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...rowItems.asMap().entries.map((entry) {
            TransactionOption option = entry.value;
            return Expanded(
              flex: 1,
              child: _buildTransactionOptionWidget(option),
            );
          }),

          if (rowItems.length < 4) ...[
            for (int i = rowItems.length; i < 4; i++)
              const Expanded(flex: 1, child: SizedBox.shrink()),
          ],
        ],
      ),
    );
  }

  Widget _buildTransactionOptionWidget(TransactionOption option) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        // TODO: Navigate to respective transaction screen based on option.title
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon container with fixed positioning
          SizedBox(
            height: 48,
            width: double.infinity,
            child: Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: option.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(option.icon, color: option.color, size: 24),
                  ),
                  if (option.hasCrown)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                    ),
                  if (option.isNew)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const AppText(
                          'NEW',
                          fontSize: 7,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 6),
          // Text with fixed height container
          SizedBox(
            height: 26,
            width: 65,
            child: AppText(
              option.title,
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    final bool isSelected = _selectedFilter == label;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.transparent,
          borderRadius: BorderRadius.circular(7),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
          ),
        ),
        child: AppText(
          label,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildUnlimitedReportsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.star, color: Colors.orange, size: 20),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: AppText(
              'View unlimited reports',
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
        ],
      ),
    );
  }

  Widget _buildTransactionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Transactions Header with staggered animation
        AnimationConfiguration.staggeredList(
          position: 0,
          duration: const Duration(milliseconds: 350),
          child: SlideAnimation(
            horizontalOffset: -20.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Transactions',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: _showDateFilterBottomSheet,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: Colors.blue,
                          ),
                          const SizedBox(width: 4),
                          AppText(
                            _selectedDateFilter,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.blue,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Animated Transaction Items
        AnimationLimiter(
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _dummyTransactions.length,
            itemBuilder: (context, index) {
              final transaction = _dummyTransactions[index];
              return AnimationConfiguration.staggeredList(
                position: index + 1,
                duration: const Duration(milliseconds: 300),
                delay: const Duration(milliseconds: 50),
                child: SlideAnimation(
                  verticalOffset: 20.0,
                  curve: Curves.easeOutCubic,
                  child: FadeInAnimation(
                    curve: Curves.easeOutCubic,
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: index < _dummyTransactions.length - 1 ? 12 : 0,
                      ),
                      child: buildTransactionItem(
                        name: transaction['name'],
                        description: transaction['description'],
                        amount: transaction['amount'],
                        date: transaction['date'],
                        status: transaction['status'],
                        statusColor: transaction['statusColor'],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showDateFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey, width: 0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Select Date',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(Icons.close, size: 24),
                  ),
                ],
              ),
            ),
            // Date options
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildDateOption('Last Week', '30 Jun 2025 - 06 Jul 2025'),
                  _buildDateOption('Last 7 days', '02 Jul 2025 - 08 Jul 2025'),
                  _buildDateOption('This month', '01 Jul 2025 - 31 Jul 2025'),
                  _buildDateOption('Last Month', '01 Jun 2025 - 30 Jun 2025'),
                  _buildDateOption(
                    'This quarter',
                    '01 Jul 2025 - 30 Sept 2025',
                  ),
                  _buildDateOption('Last quarter', '01 Apr 2025 - 30 Jun 2025'),
                  _buildDateOption(
                    'Current fiscal year',
                    '01 Apr 2025 - 31 Mar 2026',
                  ),
                  _buildDateOption(
                    'Previous fiscal year',
                    '01 Apr 2024 - 31 Mar 2025',
                  ),
                  _buildDateOption(
                    'Last 365 Days',
                    '08 Jul 2024 - 08 Jul 2025',
                  ),
                  _buildDateOption('Custom', ''),
                  SizedBox(height: 15),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateOption(String title, String dateRange) {
    final isSelected = _selectedDateFilter == title.toUpperCase();
    final isCustom = title == 'Custom';

    return GestureDetector(
      onTap: () async {
        if (isCustom) {
          Navigator.pop(context);
          await _showCustomDatePicker();
        } else {
          setState(() {
            _selectedDateFilter = title.toUpperCase();
          });
          Navigator.pop(context);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey, width: 0.1)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(title, fontSize: 16, fontWeight: FontWeight.w500),
                  if (dateRange.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    AppText(dateRange, fontSize: 14, color: Colors.grey[600]),
                  ],
                ],
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey,
                  width: 2,
                ),
                color: isSelected ? Colors.blue : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 14)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showCustomDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _customDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Colors.blue),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _customDateRange = picked;
        _selectedDateFilter = 'CUSTOM RANGE';
      });
    }
  }
}
