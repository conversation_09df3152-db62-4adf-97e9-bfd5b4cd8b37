import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/router/app_routes.dart';
import '../../../core/widgets/app_text.dart';
import '../bloc/item_reports_bloc.dart';
import '../bloc/item_reports_event.dart';
import '../bloc/item_reports_state.dart';

class ItemReportsScreen extends StatelessWidget {
  const ItemReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => ItemReportsBloc()..add(const LoadItemReports()),
      child: const ItemReportsView(),
    );
  }
}

class ItemReportsView extends StatelessWidget {
  const ItemReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: BlocBuilder<ItemReportsBloc, ItemReportsState>(
                builder: (context, state) {
                  switch (state.status) {
                    case ItemReportsStatus.loading:
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    case ItemReportsStatus.loaded:
                      return _buildReportsList(context, state.reportItems);
                    case ItemReportsStatus.error:
                      return _buildErrorState(context, state.errorMessage);
                    case ItemReportsStatus.initial:
                      return const SizedBox.shrink();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  style: const ButtonStyle(
                    padding: WidgetStatePropertyAll(EdgeInsets.zero),
                    minimumSize: WidgetStatePropertyAll(Size.zero),
                    visualDensity: VisualDensity.compact,
                  ),
                  icon: Icon(
                    Platform.isIOS
                        ? Icons.arrow_back_ios_new_rounded
                        : Icons.arrow_back,
                    color: Colors.black,
                  ),
                ),
                const Expanded(
                  child: AppText(
                    'Item Reports',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    textAlign: TextAlign.center,
                  ),
                ),
                // Invisible spacer to center the title
                const SizedBox(width: 48),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportsList(
    BuildContext context,
    List<ItemReportItem> reportItems,
  ) {
    return AnimationLimiter(
      child: ListView.separated(
        itemCount: reportItems.length,
        itemBuilder: (context, index) {
          final reportItem = reportItems[index];
          return AnimationConfiguration.staggeredList(
            position: index + 1,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildReportItem(context, reportItem),
              ),
            ),
          );
        },
        separatorBuilder: (context, index) =>
            Divider(height: 1, thickness: 1, color: Colors.grey[200]!),
      ),
    );
  }

  Widget _buildReportItem(BuildContext context, ItemReportItem reportItem) {
    return Material(
      color: Colors.white,
      elevation: 0,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          context.read<ItemReportsBloc>().add(
            ItemReportItemTapped(reportItem.type),
          );
          _handleReportItemTap(context, reportItem.type);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      reportItem.title,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    if (reportItem.description != null) ...[
                      const SizedBox(height: 4),
                      AppText(
                        reportItem.description!,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Colors.grey[600]!,
                        maxLines: 2,
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.arrow_forward_ios, size: 16, color: AppColors.primary),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          AppText(
            'Something went wrong',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          if (errorMessage != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: AppText(
                errorMessage,
                fontSize: 14,
                color: Colors.grey[500],
                textAlign: TextAlign.center,
                maxLines: 3,
              ),
            ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.read<ItemReportsBloc>().add(const RefreshItemReports());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const AppText(
              'Try Again',
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _handleReportItemTap(BuildContext context, String reportType) {
    if (reportType == 'stock_summary') {
      Navigator.pushNamed(context, StockSummaryRoute.name);
    } else {
      // Handle navigation to specific report screens
      // For now, show a placeholder message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: AppText(
            'Opening $reportType report...',
            color: Colors.white,
            fontSize: 14,
          ),
          backgroundColor: AppColors.primary,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
