import 'package:equatable/equatable.dart';

enum PartyType { customer, supplier }

class Party extends Equatable {
  final String? id;
  final String name;
  final String? contactNumber;
  final PartyType type;
  final String? gstNumber;
  final String? panNumber;
  final String? billingAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Party({
    this.id,
    required this.name,
    this.contactNumber,
    required this.type,
    this.gstNumber,
    this.panNumber,
    this.billingAddress,
    this.createdAt,
    this.updatedAt,
  });

  Party copyWith({
    String? id,
    String? name,
    String? contactNumber,
    PartyType? type,
    String? gstNumber,
    String? panNumber,
    String? billingAddress,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Party(
      id: id ?? this.id,
      name: name ?? this.name,
      contactNumber: contactNumber ?? this.contactNumber,
      type: type ?? this.type,
      gstNumber: gstNumber ?? this.gstNumber,
      panNumber: panNumber ?? this.panNumber,
      billingAddress: billingAddress ?? this.billingAddress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'contactNumber': contactNumber,
      'type': type.name,
      'gstNumber': gstNumber,
      'panNumber': panNumber,
      'billingAddress': billingAddress,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Party.fromJson(Map<String, dynamic> json) {
    return Party(
      id: json['id'],
      name: json['name'],
      contactNumber: json['contactNumber'],
      type: PartyType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PartyType.customer,
      ),
      gstNumber: json['gstNumber'],
      panNumber: json['panNumber'],
      billingAddress: json['billingAddress'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        contactNumber,
        type,
        gstNumber,
        panNumber,
        billingAddress,
        createdAt,
        updatedAt,
      ];
}
