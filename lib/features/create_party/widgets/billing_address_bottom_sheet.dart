import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/create_party_bloc.dart';
import '../bloc/create_party_event.dart';
import '../bloc/create_party_state.dart';
import 'state_selection_bottom_sheet.dart';

class BillingAddressBottomSheet extends StatefulWidget {
  const BillingAddressBottomSheet({super.key});

  @override
  State<BillingAddressBottomSheet> createState() =>
      _BillingAddressBottomSheetState();
}

class _BillingAddressBottomSheetState extends State<BillingAddressBottomSheet> {
  late TextEditingController _streetController;
  late TextEditingController _pincodeController;
  late TextEditingController _cityController;

  @override
  void initState() {
    super.initState();
    final state = context.read<CreatePartyBloc>().state;
    _streetController = TextEditingController(text: state.streetAddress);
    _pincodeController = TextEditingController(text: state.pincode);
    _cityController = TextEditingController(text: state.city);
  }

  @override
  void dispose() {
    _streetController.dispose();
    _pincodeController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStreetAddressField(),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(child: _buildStateDropdown()),
                        const SizedBox(width: 16),
                        Expanded(child: _buildPincodeField()),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildCityField(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Add Billing Address',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).pop();
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, color: Colors.grey, size: 24),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreetAddressField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                AppText(
                  'Street Address',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                AppText(
                  '*',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _streetController,
              onChanged: (value) {
                context.read<CreatePartyBloc>().add(
                  StreetAddressChanged(value),
                );
              },
              decoration: InputDecoration(
                hintText: 'Ex: 15, Hill View Apt, Lbsm Marg, Vikhroli, Mumbai',
                hintStyle: TextStyle(color: Colors.grey[400]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF5A67D8)),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 2,
            ),
            if (state.fieldErrors.containsKey('streetAddress'))
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: AppText(
                  state.fieldErrors['streetAddress']!,
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildStateDropdown() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'State',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                _showStateSelectionBottomSheet(context, state.state);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: AppText(
                        state.state.isEmpty ? 'Ex: Maharashtra' : state.state,
                        fontSize: 14,
                        color: state.state.isEmpty
                            ? Colors.grey[400]
                            : Colors.black87,
                      ),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[400],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showStateSelectionBottomSheet(
    BuildContext context,
    String currentState,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: context.read<CreatePartyBloc>(),
        child: StateSelectionBottomSheet(currentState: currentState),
      ),
    );
  }

  Widget _buildPincodeField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'Pincode',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _pincodeController,
              keyboardType: TextInputType.number,
              onChanged: (value) {
                context.read<CreatePartyBloc>().add(PincodeChanged(value));
              },
              decoration: InputDecoration(
                hintText: 'Ex: 560076',
                hintStyle: TextStyle(color: Colors.grey[400]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF5A67D8)),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            if (state.fieldErrors.containsKey('pincode'))
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: AppText(
                  state.fieldErrors['pincode']!,
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildCityField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'City',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _cityController,
              onChanged: (value) {
                context.read<CreatePartyBloc>().add(CityChanged(value));
              },
              decoration: InputDecoration(
                hintText: 'Ex: Bengaluru',
                hintStyle: TextStyle(color: Colors.grey[400]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF5A67D8)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            context.read<CreatePartyBloc>().add(const SaveBillingAddress());
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF5A67D8),
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: const AppText(
            'Save',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
