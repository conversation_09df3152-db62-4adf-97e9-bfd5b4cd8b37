import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/widgets/app_text.dart';
import '../bloc/create_party_bloc.dart';
import '../bloc/create_party_event.dart';
import '../bloc/create_party_state.dart';
import '../widgets/billing_address_bottom_sheet.dart';
import '../widgets/shipping_address_bottom_sheet.dart';

class BusinessInfoTab extends StatefulWidget {
  const BusinessInfoTab({super.key});

  @override
  State<BusinessInfoTab> createState() => _BusinessInfoTabState();
}

class _BusinessInfoTabState extends State<BusinessInfoTab> {
  // Text controllers
  final _gstController = TextEditingController();
  final _panController = TextEditingController();

  @override
  void dispose() {
    _gstController.dispose();
    _panController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            _buildGstField(),
            const SizedBox(height: 16),
            _buildPanField(),
            const SizedBox(height: 16),
            _buildBillingAddressField(),
            const SizedBox(height: 8),
            _buildShippingAddressField(),
          ],
        ),
      ),
    );
  }

  Widget _buildGstField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const AppText(
                'GST Number',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _gstController,
                onChanged: (value) {
                  context.read<CreatePartyBloc>().add(GstNumberChanged(value));
                },
                decoration: InputDecoration(
                  hintText: 'Ex: 24**********1ZM',
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[400]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[400]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF5A67D8)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
              if (state.fieldErrors.containsKey('gstNumber'))
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: AppText(
                    state.fieldErrors['gstNumber']!,
                    fontSize: 12,
                    color: Colors.red,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPanField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const AppText(
                'PAN Number',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _panController,
                onChanged: (value) {
                  context.read<CreatePartyBloc>().add(PanNumberChanged(value));
                },
                decoration: InputDecoration(
                  hintText: 'Ex: **********',
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[400]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[400]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF5A67D8)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
              if (state.fieldErrors.containsKey('panNumber'))
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: AppText(
                    state.fieldErrors['panNumber']!,
                    fontSize: 12,
                    color: Colors.red,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBillingAddressField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showBillingAddressBottomSheet(context);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: const BoxDecoration(color: Colors.white),
            child: Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: Color(0xFF5A67D8),
                  size: 20,
                ),
                const SizedBox(width: 12),
                const AppText(
                  'Billing Address',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: state.billingAddress.isNotEmpty
                      ? AppText(
                          state.billingAddress,
                          fontSize: 12,
                          color: Colors.grey[600],
                          textAlign: TextAlign.right,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        )
                      : const SizedBox.shrink(),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShippingAddressField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        // Only show if billing address exists
        if (state.billingAddress.isEmpty) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showShippingAddressBottomSheet(context);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            decoration: const BoxDecoration(color: Colors.white),
            child: Row(
              children: [
                const Icon(
                  Icons.local_shipping,
                  color: Color(0xFF5A67D8),
                  size: 20,
                ),
                const SizedBox(width: 12),
                const AppText(
                  'Shipping Address',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AppText(
                    state.shippingAddress.isNotEmpty
                        ? state.shippingAddress
                        : state.billingAddress,
                    fontSize: 12,
                    color: Colors.grey[600],
                    textAlign: TextAlign.right,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showBillingAddressBottomSheet(BuildContext context) {
    final bloc = context.read<CreatePartyBloc>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: bloc,
        child: const BillingAddressBottomSheet(),
      ),
    );
  }

  void _showShippingAddressBottomSheet(BuildContext context) {
    final bloc = context.read<CreatePartyBloc>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: bloc,
        child: const ShippingAddressBottomSheet(),
      ),
    );
  }
}
