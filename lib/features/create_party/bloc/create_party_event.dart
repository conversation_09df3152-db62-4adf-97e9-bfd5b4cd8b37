import 'package:equatable/equatable.dart';
import '../models/party.dart';

abstract class CreatePartyEvent extends Equatable {
  const CreatePartyEvent();

  @override
  List<Object?> get props => [];
}

class InitializeCreateParty extends CreatePartyEvent {
  const InitializeCreateParty();
}

class PartyNameChanged extends CreatePartyEvent {
  final String name;

  const PartyNameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class ContactNumberChanged extends CreatePartyEvent {
  final String contactNumber;

  const ContactNumberChanged(this.contactNumber);

  @override
  List<Object?> get props => [contactNumber];
}

class PartyTypeChanged extends CreatePartyEvent {
  final PartyType type;

  const PartyTypeChanged(this.type);

  @override
  List<Object?> get props => [type];
}

class GstNumberChanged extends CreatePartyEvent {
  final String gstNumber;

  const GstNumberChanged(this.gstNumber);

  @override
  List<Object?> get props => [gstNumber];
}

class PanNumberChanged extends CreatePartyEvent {
  final String panNumber;

  const PanNumberChanged(this.panNumber);

  @override
  List<Object?> get props => [panNumber];
}

class BillingAddressChanged extends CreatePartyEvent {
  final String billingAddress;

  const BillingAddressChanged(this.billingAddress);

  @override
  List<Object?> get props => [billingAddress];
}

class StreetAddressChanged extends CreatePartyEvent {
  final String streetAddress;

  const StreetAddressChanged(this.streetAddress);

  @override
  List<Object?> get props => [streetAddress];
}

class StateChanged extends CreatePartyEvent {
  final String state;

  const StateChanged(this.state);

  @override
  List<Object?> get props => [state];
}

class PincodeChanged extends CreatePartyEvent {
  final String pincode;

  const PincodeChanged(this.pincode);

  @override
  List<Object?> get props => [pincode];
}

class CityChanged extends CreatePartyEvent {
  final String city;

  const CityChanged(this.city);

  @override
  List<Object?> get props => [city];
}

class SaveBillingAddress extends CreatePartyEvent {
  const SaveBillingAddress();
}

class ShippingNameChanged extends CreatePartyEvent {
  const ShippingNameChanged(this.shippingName);

  final String shippingName;

  @override
  List<Object> get props => [shippingName];
}

class ShippingStreetAddressChanged extends CreatePartyEvent {
  const ShippingStreetAddressChanged(this.shippingStreetAddress);

  final String shippingStreetAddress;

  @override
  List<Object> get props => [shippingStreetAddress];
}

class ShippingStateChanged extends CreatePartyEvent {
  const ShippingStateChanged(this.shippingState);

  final String shippingState;

  @override
  List<Object> get props => [shippingState];
}

class ShippingPincodeChanged extends CreatePartyEvent {
  const ShippingPincodeChanged(this.shippingPincode);

  final String shippingPincode;

  @override
  List<Object> get props => [shippingPincode];
}

class ShippingCityChanged extends CreatePartyEvent {
  const ShippingCityChanged(this.shippingCity);

  final String shippingCity;

  @override
  List<Object> get props => [shippingCity];
}

class SaveShippingAddress extends CreatePartyEvent {
  const SaveShippingAddress();

  @override
  List<Object> get props => [];
}

class TabChanged extends CreatePartyEvent {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class CreatePartySubmitted extends CreatePartyEvent {
  const CreatePartySubmitted();
}

class OpeningBalanceChanged extends CreatePartyEvent {
  final double openingBalance;

  const OpeningBalanceChanged(this.openingBalance);

  @override
  List<Object?> get props => [openingBalance];
}

class BalanceTypeChanged extends CreatePartyEvent {
  final String balanceType;

  const BalanceTypeChanged(this.balanceType);

  @override
  List<Object?> get props => [balanceType];
}

class CreditPeriodChanged extends CreatePartyEvent {
  final int creditPeriodDays;

  const CreditPeriodChanged(this.creditPeriodDays);

  @override
  List<Object?> get props => [creditPeriodDays];
}

class CreditLimitChanged extends CreatePartyEvent {
  final double creditLimit;

  const CreditLimitChanged(this.creditLimit);

  @override
  List<Object?> get props => [creditLimit];
}

class ValidateForm extends CreatePartyEvent {
  const ValidateForm();
}

class PartyCategoryChanged extends CreatePartyEvent {
  final String partyCategory;

  const PartyCategoryChanged(this.partyCategory);

  @override
  List<Object?> get props => [partyCategory];
}

class AddCustomField extends CreatePartyEvent {
  final String fieldName;
  final String fieldValue;

  const AddCustomField(this.fieldName, this.fieldValue);

  @override
  List<Object?> get props => [fieldName, fieldValue];
}

class UpdateCustomField extends CreatePartyEvent {
  final String fieldName;
  final String fieldValue;

  const UpdateCustomField(this.fieldName, this.fieldValue);

  @override
  List<Object?> get props => [fieldName, fieldValue];
}

class RemoveCustomField extends CreatePartyEvent {
  final String fieldName;

  const RemoveCustomField(this.fieldName);

  @override
  List<Object?> get props => [fieldName];
}
