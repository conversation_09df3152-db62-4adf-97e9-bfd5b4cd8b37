import 'package:equatable/equatable.dart';
import '../models/party.dart';

enum CreatePartyStatus { initial, loading, success, failure }

class CreatePartyState extends Equatable {
  final CreatePartyStatus status;
  final String name;
  final String contactNumber;
  final PartyType partyType;
  final String gstNumber;
  final String panNumber;
  final String billingAddress;
  final String streetAddress;
  final String state;
  final String pincode;
  final String city;
  final String shippingName;
  final String shippingStreetAddress;
  final String shippingState;
  final String shippingPincode;
  final String shippingCity;
  final String shippingAddress;
  final double openingBalance;
  final String balanceType; // 'receive' or 'pay'
  final int creditPeriodDays;
  final double creditLimit;
  final int currentTabIndex;
  final String partyCategory;
  final Map<String, String> customFields;
  final String? errorMessage;
  final Map<String, String> fieldErrors;
  final bool isFormValid;

  const CreatePartyState({
    this.status = CreatePartyStatus.initial,
    this.name = '',
    this.contactNumber = '',
    this.partyType = PartyType.customer,
    this.gstNumber = '',
    this.panNumber = '',
    this.billingAddress = '',
    this.streetAddress = '',
    this.state = '',
    this.pincode = '',
    this.city = '',
    this.shippingName = '',
    this.shippingStreetAddress = '',
    this.shippingState = '',
    this.shippingPincode = '',
    this.shippingCity = '',
    this.shippingAddress = '',
    this.openingBalance = 0.0,
    this.balanceType = 'receive',
    this.creditPeriodDays = 7,
    this.creditLimit = 0.0,
    this.currentTabIndex = 0,
    this.partyCategory = '',
    this.customFields = const {},
    this.errorMessage,
    this.fieldErrors = const {},
    this.isFormValid = false,
  });

  CreatePartyState copyWith({
    CreatePartyStatus? status,
    String? name,
    String? contactNumber,
    PartyType? partyType,
    String? gstNumber,
    String? panNumber,
    String? billingAddress,
    String? streetAddress,
    String? state,
    String? pincode,
    String? city,
    String? shippingName,
    String? shippingStreetAddress,
    String? shippingState,
    String? shippingPincode,
    String? shippingCity,
    String? shippingAddress,
    double? openingBalance,
    String? balanceType,
    int? creditPeriodDays,
    double? creditLimit,
    int? currentTabIndex,
    String? partyCategory,
    Map<String, String>? customFields,
    String? errorMessage,
    Map<String, String>? fieldErrors,
    bool? isFormValid,
  }) {
    return CreatePartyState(
      status: status ?? this.status,
      name: name ?? this.name,
      contactNumber: contactNumber ?? this.contactNumber,
      partyType: partyType ?? this.partyType,
      gstNumber: gstNumber ?? this.gstNumber,
      panNumber: panNumber ?? this.panNumber,
      billingAddress: billingAddress ?? this.billingAddress,
      streetAddress: streetAddress ?? this.streetAddress,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      city: city ?? this.city,
      shippingName: shippingName ?? this.shippingName,
      shippingStreetAddress:
          shippingStreetAddress ?? this.shippingStreetAddress,
      shippingState: shippingState ?? this.shippingState,
      shippingPincode: shippingPincode ?? this.shippingPincode,
      shippingCity: shippingCity ?? this.shippingCity,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      openingBalance: openingBalance ?? this.openingBalance,
      balanceType: balanceType ?? this.balanceType,
      creditPeriodDays: creditPeriodDays ?? this.creditPeriodDays,
      creditLimit: creditLimit ?? this.creditLimit,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      partyCategory: partyCategory ?? this.partyCategory,
      customFields: customFields ?? this.customFields,
      errorMessage: errorMessage ?? this.errorMessage,
      fieldErrors: fieldErrors ?? this.fieldErrors,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  Party toParty() {
    // Combine address fields into a single billing address string
    final addressParts = <String>[];
    if (streetAddress.isNotEmpty) addressParts.add(streetAddress);
    if (city.isNotEmpty) addressParts.add(city);
    if (state.isNotEmpty) addressParts.add(state);
    if (pincode.isNotEmpty) addressParts.add(pincode);

    final fullAddress = addressParts.isNotEmpty
        ? addressParts.join(', ')
        : billingAddress;

    return Party(
      name: name,
      contactNumber: contactNumber.isNotEmpty ? contactNumber : null,
      type: partyType,
      gstNumber: gstNumber.isNotEmpty ? gstNumber : null,
      panNumber: panNumber.isNotEmpty ? panNumber : null,
      billingAddress: fullAddress.isNotEmpty ? fullAddress : null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    status,
    name,
    contactNumber,
    partyType,
    gstNumber,
    panNumber,
    billingAddress,
    streetAddress,
    state,
    pincode,
    city,
    shippingName,
    shippingStreetAddress,
    shippingState,
    shippingPincode,
    shippingCity,
    shippingAddress,
    openingBalance,
    balanceType,
    creditPeriodDays,
    creditLimit,
    currentTabIndex,
    partyCategory,
    customFields,
    errorMessage,
    fieldErrors,
    isFormValid,
  ];
}
