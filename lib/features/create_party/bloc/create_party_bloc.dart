import 'package:flutter_bloc/flutter_bloc.dart';
import 'create_party_event.dart';
import 'create_party_state.dart';

class CreatePartyBloc extends Bloc<CreatePartyEvent, CreatePartyState> {
  CreatePartyBloc() : super(const CreatePartyState()) {
    on<InitializeCreateParty>(_onInitializeCreateParty);
    on<PartyNameChanged>(_onPartyNameChanged);
    on<ContactNumberChanged>(_onContactNumberChanged);
    on<PartyTypeChanged>(_onPartyTypeChanged);
    on<GstNumberChanged>(_onGstNumberChanged);
    on<PanNumberChanged>(_onPanNumberChanged);
    on<BillingAddressChanged>(_onBillingAddressChanged);
    on<StreetAddressChanged>(_onStreetAddressChanged);
    on<StateChanged>(_onStateChanged);
    on<PincodeChanged>(_onPincodeChanged);
    on<CityChanged>(_onCityChanged);
    on<SaveBillingAddress>(_onSaveBillingAddress);
    on<ShippingNameChanged>(_onShippingNameChanged);
    on<ShippingStreetAddressChanged>(_onShippingStreetAddressChanged);
    on<ShippingStateChanged>(_onShippingStateChanged);
    on<ShippingPincodeChanged>(_onShippingPincodeChanged);
    on<ShippingCityChanged>(_onShippingCityChanged);
    on<SaveShippingAddress>(_onSaveShippingAddress);
    on<TabChanged>(_onTabChanged);
    on<OpeningBalanceChanged>(_onOpeningBalanceChanged);
    on<BalanceTypeChanged>(_onBalanceTypeChanged);
    on<CreditPeriodChanged>(_onCreditPeriodChanged);
    on<CreditLimitChanged>(_onCreditLimitChanged);
    on<CreatePartySubmitted>(_onCreatePartySubmitted);
    on<ValidateForm>(_onValidateForm);
    on<PartyCategoryChanged>(_onPartyCategoryChanged);
    on<AddCustomField>(_onAddCustomField);
    on<UpdateCustomField>(_onUpdateCustomField);
    on<RemoveCustomField>(_onRemoveCustomField);
  }

  void _onInitializeCreateParty(
    InitializeCreateParty event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(const CreatePartyState());
  }

  void _onPartyNameChanged(
    PartyNameChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.name.trim().isEmpty) {
      updatedErrors['name'] = 'Party name is required';
    } else {
      updatedErrors.remove('name');
    }

    emit(
      state.copyWith(
        name: event.name,
        fieldErrors: updatedErrors,
        isFormValid: _isFormValid(
          state.copyWith(name: event.name, fieldErrors: updatedErrors),
        ),
      ),
    );
  }

  void _onContactNumberChanged(
    ContactNumberChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.contactNumber.isNotEmpty &&
        !_isValidPhoneNumber(event.contactNumber)) {
      updatedErrors['contactNumber'] = 'Please enter a valid contact number';
    } else {
      updatedErrors.remove('contactNumber');
    }

    emit(
      state.copyWith(
        contactNumber: event.contactNumber,
        fieldErrors: updatedErrors,
        isFormValid: _isFormValid(
          state.copyWith(
            contactNumber: event.contactNumber,
            fieldErrors: updatedErrors,
          ),
        ),
      ),
    );
  }

  void _onPartyTypeChanged(
    PartyTypeChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(partyType: event.type));
  }

  void _onGstNumberChanged(
    GstNumberChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.gstNumber.isNotEmpty && !_isValidGstNumber(event.gstNumber)) {
      updatedErrors['gstNumber'] = 'Please enter a valid GST number';
    } else {
      updatedErrors.remove('gstNumber');
    }

    emit(
      state.copyWith(
        gstNumber: event.gstNumber,
        fieldErrors: updatedErrors,
        isFormValid: _isFormValid(
          state.copyWith(
            gstNumber: event.gstNumber,
            fieldErrors: updatedErrors,
          ),
        ),
      ),
    );
  }

  void _onPanNumberChanged(
    PanNumberChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.panNumber.isNotEmpty && !_isValidPanNumber(event.panNumber)) {
      updatedErrors['panNumber'] = 'Please enter a valid PAN number';
    } else {
      updatedErrors.remove('panNumber');
    }

    emit(
      state.copyWith(
        panNumber: event.panNumber,
        fieldErrors: updatedErrors,
        isFormValid: _isFormValid(
          state.copyWith(
            panNumber: event.panNumber,
            fieldErrors: updatedErrors,
          ),
        ),
      ),
    );
  }

  void _onBillingAddressChanged(
    BillingAddressChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(billingAddress: event.billingAddress));
  }

  void _onTabChanged(TabChanged event, Emitter<CreatePartyState> emit) {
    emit(state.copyWith(currentTabIndex: event.tabIndex));
  }

  void _onOpeningBalanceChanged(
    OpeningBalanceChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.openingBalance < 0) {
      updatedErrors['openingBalance'] = 'Opening balance cannot be negative';
    } else {
      updatedErrors.remove('openingBalance');
    }

    emit(
      state.copyWith(
        openingBalance: event.openingBalance,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onBalanceTypeChanged(
    BalanceTypeChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(balanceType: event.balanceType));
  }

  void _onCreditPeriodChanged(
    CreditPeriodChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.creditPeriodDays <= 0) {
      updatedErrors['creditPeriod'] = 'Credit period must be greater than 0';
    } else {
      updatedErrors.remove('creditPeriod');
    }

    emit(
      state.copyWith(
        creditPeriodDays: event.creditPeriodDays,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onCreditLimitChanged(
    CreditLimitChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.creditLimit < 0) {
      updatedErrors['creditLimit'] = 'Credit limit cannot be negative';
    } else {
      updatedErrors.remove('creditLimit');
    }

    emit(
      state.copyWith(
        creditLimit: event.creditLimit,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onCreatePartySubmitted(
    CreatePartySubmitted event,
    Emitter<CreatePartyState> emit,
  ) async {
    if (!state.isFormValid) {
      add(const ValidateForm());
      return;
    }

    emit(state.copyWith(status: CreatePartyStatus.loading));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // TODO: Replace with actual API call to create party
      // final party = state.toParty();
      // await partyRepository.createParty(party);

      emit(state.copyWith(status: CreatePartyStatus.success));
    } catch (error) {
      emit(
        state.copyWith(
          status: CreatePartyStatus.failure,
          errorMessage: 'Failed to create party. Please try again.',
        ),
      );
    }
  }

  void _onValidateForm(ValidateForm event, Emitter<CreatePartyState> emit) {
    final errors = <String, String>{};

    if (state.name.trim().isEmpty) {
      errors['name'] = 'Party name is required';
    }

    if (state.contactNumber.isNotEmpty &&
        !_isValidPhoneNumber(state.contactNumber)) {
      errors['contactNumber'] = 'Please enter a valid contact number';
    }

    if (state.gstNumber.isNotEmpty && !_isValidGstNumber(state.gstNumber)) {
      errors['gstNumber'] = 'Please enter a valid GST number';
    }

    if (state.panNumber.isNotEmpty && !_isValidPanNumber(state.panNumber)) {
      errors['panNumber'] = 'Please enter a valid PAN number';
    }

    emit(state.copyWith(fieldErrors: errors, isFormValid: errors.isEmpty));
  }

  bool _isFormValid(CreatePartyState state) {
    return state.name.trim().isNotEmpty && state.fieldErrors.isEmpty;
  }

  bool _isValidPhoneNumber(String phone) {
    final phoneRegex = RegExp(r'^[+]?[0-9]{10,15}$');
    return phoneRegex.hasMatch(phone.replaceAll(RegExp(r'[\s\-\(\)]'), ''));
  }

  bool _isValidGstNumber(String gst) {
    final gstRegex = RegExp(
      r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$',
    );
    return gstRegex.hasMatch(gst.toUpperCase());
  }

  bool _isValidPanNumber(String pan) {
    final panRegex = RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$');
    return panRegex.hasMatch(pan.toUpperCase());
  }

  void _onStreetAddressChanged(
    StreetAddressChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.streetAddress.trim().isEmpty) {
      updatedErrors['streetAddress'] = 'Street address is required';
    } else {
      updatedErrors.remove('streetAddress');
    }

    emit(
      state.copyWith(
        streetAddress: event.streetAddress,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onStateChanged(StateChanged event, Emitter<CreatePartyState> emit) {
    emit(state.copyWith(state: event.state));
  }

  void _onPincodeChanged(PincodeChanged event, Emitter<CreatePartyState> emit) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.pincode.isNotEmpty && !_isValidPincode(event.pincode)) {
      updatedErrors['pincode'] = 'Please enter a valid pincode';
    } else {
      updatedErrors.remove('pincode');
    }

    emit(state.copyWith(pincode: event.pincode, fieldErrors: updatedErrors));
  }

  void _onCityChanged(CityChanged event, Emitter<CreatePartyState> emit) {
    emit(state.copyWith(city: event.city));
  }

  void _onSaveBillingAddress(
    SaveBillingAddress event,
    Emitter<CreatePartyState> emit,
  ) {
    // Validate address fields
    final errors = <String, String>{};

    if (state.streetAddress.trim().isEmpty) {
      errors['streetAddress'] = 'Street address is required';
    }

    if (state.pincode.isNotEmpty && !_isValidPincode(state.pincode)) {
      errors['pincode'] = 'Please enter a valid pincode';
    }

    if (errors.isNotEmpty) {
      emit(state.copyWith(fieldErrors: {...state.fieldErrors, ...errors}));
      return;
    }

    // Combine address fields into billing address
    final addressParts = <String>[];
    if (state.streetAddress.isNotEmpty) addressParts.add(state.streetAddress);
    if (state.city.isNotEmpty) addressParts.add(state.city);
    if (state.state.isNotEmpty) addressParts.add(state.state);
    if (state.pincode.isNotEmpty) addressParts.add(state.pincode);

    final fullAddress = addressParts.join(', ');

    emit(
      state.copyWith(
        billingAddress: fullAddress,
        fieldErrors: Map<String, String>.from(state.fieldErrors)
          ..removeWhere(
            (key, value) => ['streetAddress', 'pincode'].contains(key),
          ),
      ),
    );
  }

  bool _isValidPincode(String pincode) {
    final pincodeRegex = RegExp(r'^[1-9][0-9]{5}$');
    return pincodeRegex.hasMatch(pincode);
  }

  void _onShippingNameChanged(
    ShippingNameChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(shippingName: event.shippingName));
  }

  void _onShippingStreetAddressChanged(
    ShippingStreetAddressChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.shippingStreetAddress.trim().isEmpty) {
      updatedErrors['shippingStreetAddress'] = 'Street address is required';
    } else {
      updatedErrors.remove('shippingStreetAddress');
    }

    emit(
      state.copyWith(
        shippingStreetAddress: event.shippingStreetAddress,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onShippingStateChanged(
    ShippingStateChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(shippingState: event.shippingState));
  }

  void _onShippingPincodeChanged(
    ShippingPincodeChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (event.shippingPincode.isNotEmpty &&
        !_isValidPincode(event.shippingPincode)) {
      updatedErrors['shippingPincode'] = 'Please enter a valid pincode';
    } else {
      updatedErrors.remove('shippingPincode');
    }

    emit(
      state.copyWith(
        shippingPincode: event.shippingPincode,
        fieldErrors: updatedErrors,
      ),
    );
  }

  void _onShippingCityChanged(
    ShippingCityChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    emit(state.copyWith(shippingCity: event.shippingCity));
  }

  void _onSaveShippingAddress(
    SaveShippingAddress event,
    Emitter<CreatePartyState> emit,
  ) {
    // Validate address fields
    final errors = <String, String>{};

    if (state.shippingStreetAddress.trim().isEmpty) {
      errors['shippingStreetAddress'] = 'Street address is required';
    }

    if (state.shippingPincode.isNotEmpty &&
        !_isValidPincode(state.shippingPincode)) {
      errors['shippingPincode'] = 'Please enter a valid pincode';
    }

    if (errors.isNotEmpty) {
      emit(state.copyWith(fieldErrors: {...state.fieldErrors, ...errors}));
      return;
    }

    // Combine address fields into shipping address
    final addressParts = <String>[];
    if (state.shippingStreetAddress.isNotEmpty) {
      addressParts.add(state.shippingStreetAddress);
    }
    if (state.shippingCity.isNotEmpty) {
      addressParts.add(state.shippingCity);
    }
    if (state.shippingState.isNotEmpty) {
      addressParts.add(state.shippingState);
    }
    if (state.shippingPincode.isNotEmpty) {
      addressParts.add(state.shippingPincode);
    }

    final fullAddress = addressParts.join(', ');

    emit(
      state.copyWith(
        shippingAddress: fullAddress,
        fieldErrors: Map<String, String>.from(state.fieldErrors)
          ..removeWhere(
            (key, value) =>
                ['shippingStreetAddress', 'shippingPincode'].contains(key),
          ),
      ),
    );
  }

  void _onPartyCategoryChanged(
    PartyCategoryChanged event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    updatedErrors.remove('partyCategory');

    emit(
      state.copyWith(
        partyCategory: event.partyCategory,
        fieldErrors: updatedErrors,
        isFormValid: _isFormValid(
          state.copyWith(
            partyCategory: event.partyCategory,
            fieldErrors: updatedErrors,
          ),
        ),
      ),
    );
  }

  void _onAddCustomField(AddCustomField event, Emitter<CreatePartyState> emit) {
    final updatedCustomFields = Map<String, String>.from(state.customFields);
    updatedCustomFields[event.fieldName] = event.fieldValue;

    emit(
      state.copyWith(
        customFields: updatedCustomFields,
        isFormValid: _isFormValid(
          state.copyWith(customFields: updatedCustomFields),
        ),
      ),
    );
  }

  void _onUpdateCustomField(
    UpdateCustomField event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedCustomFields = Map<String, String>.from(state.customFields);
    if (updatedCustomFields.containsKey(event.fieldName)) {
      updatedCustomFields[event.fieldName] = event.fieldValue;
    }

    emit(
      state.copyWith(
        customFields: updatedCustomFields,
        isFormValid: _isFormValid(
          state.copyWith(customFields: updatedCustomFields),
        ),
      ),
    );
  }

  void _onRemoveCustomField(
    RemoveCustomField event,
    Emitter<CreatePartyState> emit,
  ) {
    final updatedCustomFields = Map<String, String>.from(state.customFields);
    updatedCustomFields.remove(event.fieldName);

    emit(
      state.copyWith(
        customFields: updatedCustomFields,
        isFormValid: _isFormValid(
          state.copyWith(customFields: updatedCustomFields),
        ),
      ),
    );
  }
}
