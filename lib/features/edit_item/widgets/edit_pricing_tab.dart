import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/edit_item_bloc.dart';
import '../bloc/edit_item_event.dart';
import '../bloc/edit_item_state.dart';
import '../models/item.dart';

class EditPricingTab extends StatefulWidget {
  final EditItemFormState state;
  final TextEditingController unitController;
  final TextEditingController salesPriceController;
  final TextEditingController purchasePriceController;
  final TextEditingController hsnController;
  final bool isService;

  const EditPricingTab({
    super.key,
    required this.state,
    required this.unitController,
    required this.salesPriceController,
    required this.purchasePriceController,
    required this.hsnController,
    this.isService = false,
  });

  @override
  State<EditPricingTab> createState() => _EditPricingTabState();
}

class _EditPricingTabState extends State<EditPricingTab> {
  // Search query for HSN filtering
  String _hsnSearchQuery = '';

  // State variables for alternative unit functionality
  String _selectedPrimaryUnit = '';
  String _selectedAlternativeUnit = '';
  String _conversionRate = '1.0';
  bool _showAlternativeUnit = false;

  // Sample unit data
  static const List<Map<String, String>> _units = [
    {'code': 'PCS', 'description': 'Pieces'},
    {'code': 'BOX', 'description': 'Box'},
    {'code': 'KG', 'description': 'Kilograms'},
    {'code': 'GRAM', 'description': 'Grams'},
    {'code': 'LITER', 'description': 'Liters'},
    {'code': 'ML', 'description': 'Milliliters'},
    {'code': 'METER', 'description': 'Meters'},
    {'code': 'CM', 'description': 'Centimeters'},
    {'code': 'DOZEN', 'description': 'Dozen'},
    {'code': 'SET', 'description': 'Set'},
    {'code': 'PAIR', 'description': 'Pair'},
    {'code': 'PACK', 'description': 'Pack'},
    {'code': 'BOTTLE', 'description': 'Bottle'},
    {'code': 'CAN', 'description': 'Can'},
    {'code': 'BAG', 'description': 'Bag'},
    {'code': 'ROLL', 'description': 'Roll'},
    {'code': 'SHEET', 'description': 'Sheet'},
    {'code': 'TUBE', 'description': 'Tube'},
    {'code': 'CARTON', 'description': 'Carton'},
    {'code': 'BUNDLE', 'description': 'Bundle'},
  ];

  // Sample HSN codes data
  static const List<Map<String, String>> _hsnCodes = [
    {
      'code': '998559',
      'description': 'Other Travel Arrangement And Related Services N.E.C',
    },
    {
      'code': '85238090',
      'description':
          'Discs, Tapes, Solid-State Non-Volatile Storage Devices, Smart Cards And Other Media For The Recording Of Sound Or Of Other Phenomena',
    },
    {
      'code': '13012000',
      'description':
          'Lac; Natural Gums, Resins, Gum-Resins And Oleoresins (For Example, Balsams); Gum Arabic',
    },
    {
      'code': '58063930',
      'description':
          'Narrow Woven Fabrics Other Than Goods Of Heading 5807; Narrow Fabrics Consisting Of Warp Without Weft Assembled By Means Of An Adhesive',
    },
    {
      'code': '4407',
      'description':
          'Wood Sawn Or Chipped Lengthwise, Sliced Or Peeled, Whether Or Not Planed, Sanded Or End-Jointed, Of A Thickness Exceeding 6 Mm',
    },
    {
      'code': '51122040',
      'description':
          'Woven Fabrics Of Combed Wool Or Of Combed Fine Animal Hair Other, Mixed Mainly Or Solely With Man-Made Filaments: Printed',
    },
    {
      'code': '09109939',
      'description':
          'Ginger, Saffron, Turmeric (Curcuma), Thyme, Bay Leaves, Curry And Other Spices Other Spices : Other : Husk : Other',
    },
    {
      'code': '26209100',
      'description':
          'Slag, Ash And Residues (Other Than From The Manufacture Of Iron Or Steel), Containing Arsenic, Metals Or Their Compounds - Other',
    },
    {
      'code': '85192900',
      'description':
          'Turntables (Record-Decks), Record-Players, Cassette-Players And Other Sound Reproducing Apparatus, Not Incorporating A Recording Device',
    },
    {
      'code': '7505',
      'description':
          'Nickel Bars, Rods, Profiles And Wire - Bars, Rods And Profiles',
    },
    {
      'code': '32041552',
      'description':
          'Synthetic Organic Colouring Matter, Whether Or Not Chemically Defined; Preparations As Specified In Note 3 To This Chapter Based On Synthetic Organic Colouring Matter',
    },
    {
      'code': '55144200',
      'description':
          'Woven Fabrics Of Synthetic Staple Fibres, Containing Less Than 85% By Weight Of Such Fibres, Mixed Mainly Or Solely With Cotton, Of A Weight Not Exceeding 170 G/M²',
    },
    {
      'code': '40012200',
      'description':
          'Natural Rubber; Balata, Gutta-Percha, Guayule, Chicle And Similar Natural Gums, In Primary Forms Or In Plates, Sheets Or Strip - Natural Rubber In Other Forms',
    },
    {'code': '720836', 'description': 'Of A Thickness Exceeding 10 Mm'},
    {
      'code': '87085000',
      'description':
          'Parts And Accessories Of The Motor Vehicles Of Headings 8701 To 8705 - Drive-Axles With Differential, Whether Or Not Provided With Other Transmission Components',
    },
    {
      'code': '85393910',
      'description':
          'Electric Filament Or Discharge Lamps, Including Sealed Beam Lamp Units And Ultra-Violet Or Infra Red Lamps; Arc-Lamps - Other',
    },
    {
      'code': '8511',
      'description':
          'Electrical Ignition Or Starting Equipment Of A Kind Used For Spark-Ignition Or Compression-Ignition Internal Combustion Engines',
    },
    {
      'code': '54026910',
      'description':
          'Synthetic Filament Yarn (Other Than Sewing Thread), Not Put Up For Retail Sale, Including Synthetic Monofilament Of Less Than 67 Decitex',
    },
    {
      'code': '16055500',
      'description':
          'Crustaceans, Molluscs And Other Aquatic Invertebrates, Prepared Or Preserved - Molluscs - Octopus',
    },
    {
      'code': '29157030',
      'description':
          'Saturated Acyclic Monocarboxylic Acids And Their Anhydrides, Halides, Peroxides And Peroxyacids; Their Halogenated, Sulphonated, Nitrated Or Nitrosated Derivatives',
    },
    {'code': '6704', 'description': 'Paper and paperboard'},
    {'code': '8517', 'description': 'Telephone sets, mobile phones'},
    {'code': '3004', 'description': 'Medicaments'},
    {'code': '1006', 'description': 'Rice'},
    {'code': '2710', 'description': 'Petroleum oils'},
  ];

  @override
  void initState() {
    super.initState();
    // Initialize with existing data from edit state
    _selectedPrimaryUnit = widget.state.unit.isNotEmpty
        ? widget.state.unit
        : 'PCS';

    // Pre-populate controllers with existing data
    widget.unitController.text = widget.state.unit;
    widget.salesPriceController.text = widget.state.salesPrice > 0
        ? widget.state.salesPrice.toString()
        : '';
    widget.purchasePriceController.text = widget.state.purchasePrice > 0
        ? widget.state.purchasePrice.toString()
        : '';
    widget.hsnController.text = widget.state.hsn;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildUnitField(context, widget.state),
              const SizedBox(height: 16),
              _buildSalesPriceField(context, widget.state),
              const SizedBox(height: 16),
              // Only show purchase price field for products, not services
              if (!widget.isService) ...[
                _buildPurchasePriceField(context, widget.state),
                const SizedBox(height: 16),
              ],
              _buildGSTField(context, widget.state),
              const SizedBox(height: 16),
              _buildHSNField(context, widget.state),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUnitField(BuildContext context, EditItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Unit',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showUnitSelectionBottomSheet(context, state.unit);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    state.unit.isEmpty ? 'Select Unit' : state.unit,
                    fontSize: 14,
                    color: state.unit.isEmpty
                        ? Colors.grey[400]!
                        : Colors.black87,
                  ),
                ),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: Color(0xFF5A67D8),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (state.showValidationErrors && state.errors.containsKey('unit')) ...[
          const SizedBox(height: 4),
          AppText(state.errors['unit']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  void _showUnitSelectionBottomSheet(BuildContext context, String currentUnit) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnitSelectionBottomSheet(currentUnit),
    );
  }

  Widget _buildUnitSelectionBottomSheet(String currentUnit) {
    return StatefulBuilder(
      builder: (context, setModalState) {
        // Initialize primary unit if not set
        if (_selectedPrimaryUnit.isEmpty) {
          _selectedPrimaryUnit = currentUnit.isNotEmpty ? currentUnit : 'PCS';
        }

        return Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildUnitBottomSheetHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Primary Unit Section
                        _buildPrimaryUnitSection(setModalState),

                        const SizedBox(height: 24),

                        // Alternative Unit Section
                        _buildAlternativeUnitSection(setModalState),
                      ],
                    ),
                  ),
                ),

                // Save Button
                _buildSaveButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnitBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Measuring Unit',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 24, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryUnitSection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Primary Unit',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.grey,
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showPrimaryUnitSelectionBottomSheet(setModalState);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    _selectedPrimaryUnit.isEmpty
                        ? 'PIECES(PCS)'
                        : '${_getUnitDescription(_selectedPrimaryUnit)}($_selectedPrimaryUnit)',
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlternativeUnitSection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!_showAlternativeUnit) ...[
          // Add Alternate Unit Button
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setModalState(() {
                _showAlternativeUnit = true;
              });
            },
            child: Row(
              children: [
                const Icon(Icons.add, color: Color(0xFF5A67D8), size: 20),
                const SizedBox(width: 8),
                const AppText(
                  'Alternate Unit',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF5A67D8),
                ),
              ],
            ),
          ),
        ] else ...[
          // Alternative Unit Fields
          const AppText(
            'Alternate Unit',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
          const SizedBox(height: 12),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _showAlternativeUnitSelectionBottomSheet(setModalState);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: AppText(
                      _selectedAlternativeUnit.isEmpty
                          ? 'Select Unit'
                          : '${_getUnitDescription(_selectedAlternativeUnit)}($_selectedAlternativeUnit)',
                      fontSize: 14,
                      color: _selectedAlternativeUnit.isEmpty
                          ? Colors.grey[400]!
                          : Colors.black87,
                    ),
                  ),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Conversion Rate Section
          const AppText(
            'Conversion Rate',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const AppText('1 ', fontSize: 14, color: Colors.black87),
              AppText(
                '($_selectedPrimaryUnit)',
                fontSize: 14,
                color: Colors.black87,
              ),
              const AppText(' = ', fontSize: 14, color: Colors.black87),
              Expanded(
                child: TextFormField(
                  initialValue: _conversionRate,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  onChanged: (value) {
                    setModalState(() {
                      _conversionRate = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: '1.0',
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: Color(0xFF5A67D8),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Remove Alternate Units Button
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setModalState(() {
                _showAlternativeUnit = false;
                _selectedAlternativeUnit = '';
                _conversionRate = '1.0';
              });
            },
            child: Row(
              children: [
                const Icon(
                  Icons.remove_circle_outline,
                  color: Color(0xFF5A67D8),
                  size: 20,
                ),
                const SizedBox(width: 8),
                const AppText(
                  'Remove Alternate Units',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF5A67D8),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            // Apply the selected unit to the form
            if (mounted) {
              context.read<EditItemBloc>().add(
                UnitChanged(_selectedPrimaryUnit),
              );
              Navigator.pop(context);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF5A67D8),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: const AppText(
            'Save',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  String _getUnitDescription(String unitCode) {
    final unit = _units.firstWhere(
      (unit) => unit['code'] == unitCode,
      orElse: () => {'description': unitCode},
    );
    return unit['description']!;
  }

  void _showPrimaryUnitSelectionBottomSheet(StateSetter parentSetState) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnitSelectionModal(
        'Select Primary Unit',
        _selectedPrimaryUnit,
        (selectedUnit) {
          parentSetState(() {
            _selectedPrimaryUnit = selectedUnit;
          });
        },
      ),
    );
  }

  void _showAlternativeUnitSelectionBottomSheet(StateSetter parentSetState) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildUnitSelectionModal(
        'Select Alternative Unit',
        _selectedAlternativeUnit,
        (selectedUnit) {
          parentSetState(() {
            _selectedAlternativeUnit = selectedUnit;
          });
        },
      ),
    );
  }

  Widget _buildUnitSelectionModal(
    String title,
    String currentSelection,
    Function(String) onUnitSelected,
  ) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: AppText(
                      title,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Unit List
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: _units.map((unitData) {
                    final bool isSelected =
                        unitData['code'] == currentSelection;
                    return GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        onUnitSelected(unitData['code']!);
                        Navigator.pop(context);
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                              : Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF5A67D8)
                                : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isSelected
                                  ? Icons.check_circle
                                  : Icons.radio_button_unchecked,
                              color: isSelected
                                  ? const Color(0xFF5A67D8)
                                  : Colors.grey[400],
                              size: 24,
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AppText(
                                    unitData['code']!,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isSelected
                                        ? const Color(0xFF5A67D8)
                                        : Colors.black87,
                                  ),
                                  const SizedBox(height: 2),
                                  AppText(
                                    unitData['description']!,
                                    fontSize: 12,
                                    color: Colors.grey[600]!,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesPriceField(BuildContext context, EditItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Sales Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: widget.salesPriceController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 0,
                      minHeight: 0,
                    ),
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 10,
                        bottom: 10,
                      ),
                      child: AppText(
                        '₹',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    hintText: '115',
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value) ?? 0.0;
                    context.read<EditItemBloc>().add(SalesPriceChanged(price));
                  },
                ),
              ),
              Builder(
                builder: (BuildContext buttonContext) {
                  return GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      _showSalesTaxMenu(buttonContext, state.salesTaxType);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AppText(
                            state.salesTaxType.displayName,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('salesPrice')) ...[
          const SizedBox(height: 8),
          AppText(state.errors['salesPrice']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  Widget _buildPurchasePriceField(
    BuildContext context,
    EditItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Purchase Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: widget.purchasePriceController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 0,
                      minHeight: 0,
                    ),
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 10,
                        bottom: 10,
                      ),
                      child: AppText(
                        '₹',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    hintText: '115',
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value) ?? 0.0;
                    context.read<EditItemBloc>().add(
                      PurchasePriceChanged(price),
                    );
                  },
                ),
              ),
              Builder(
                builder: (BuildContext buttonContext) {
                  return GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      _showPurchaseTaxMenu(
                        buttonContext,
                        state.purchaseTaxType,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AppText(
                            state.purchaseTaxType.displayName,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('purchasePrice')) ...[
          const SizedBox(height: 8),
          AppText(
            state.errors['purchasePrice']!,
            fontSize: 12,
            color: Colors.red,
          ),
        ],
      ],
    );
  }

  void _showSalesTaxMenu(BuildContext context, TaxType currentTaxType) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset(0, 30), ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset(0, 0)),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    final bloc = context.read<EditItemBloc>();

    showMenu<TaxType>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      color: Colors.white,
      items: TaxType.values.map<PopupMenuItem<TaxType>>((TaxType value) {
        final bool isSelected = value == currentTaxType;
        return PopupMenuItem<TaxType>(
          value: value,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  isSelected
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  size: 16,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                ),
              ),
              const SizedBox(width: 12),
              AppText(
                value.displayName,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        );
      }).toList(),
    ).then((value) {
      if (value == null || !mounted) return;

      HapticFeedback.lightImpact();
      bloc.add(SalesTaxTypeChanged(value));
    });
  }

  void _showPurchaseTaxMenu(BuildContext context, TaxType currentTaxType) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset(0, 30), ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset(0, 0)),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    final bloc = context.read<EditItemBloc>();

    showMenu<TaxType>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      color: Colors.white,
      items: TaxType.values.map<PopupMenuItem<TaxType>>((TaxType value) {
        final bool isSelected = value == currentTaxType;
        return PopupMenuItem<TaxType>(
          value: value,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  isSelected
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  size: 16,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                ),
              ),
              const SizedBox(width: 12),
              AppText(
                value.displayName,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        );
      }).toList(),
    ).then((value) {
      if (value == null || !mounted) return;

      HapticFeedback.lightImpact();
      bloc.add(PurchaseTaxTypeChanged(value));
    });
  }

  Widget _buildGSTField(BuildContext context, EditItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'GST',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showGSTSelectionBottomSheet(context, state.gstType);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  state.gstType.displayName,
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  color: Colors.black87,
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: const Color(0xFF5A67D8),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHSNField(BuildContext context, EditItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'HSN',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showHSNSelectionBottomSheet(context, state.hsn);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    state.hsn.isEmpty ? 'Select HSN Code' : state.hsn,
                    fontSize: 14,
                    color: state.hsn.isEmpty
                        ? Colors.grey[400]!
                        : Colors.black87,
                  ),
                ),
                const Icon(Icons.search, color: Color(0xFF5A67D8), size: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showGSTSelectionBottomSheet(
    BuildContext context,
    GSTType currentGSTType,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildGSTSelectionBottomSheet(currentGSTType),
    );
  }

  Widget _buildGSTSelectionBottomSheet(GSTType currentGSTType) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header
            _buildGSTBottomSheetHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: GSTType.values.map((gstType) {
                    final bool isSelected = gstType == currentGSTType;
                    return _buildGSTOptionItem(gstType, isSelected);
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGSTBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Select GST Type',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 24, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGSTOptionItem(GSTType gstType, bool isSelected) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        if (mounted) {
          context.read<EditItemBloc>().add(GSTTypeChanged(gstType));
          Navigator.pop(context);
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[400],
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AppText(
                gstType.displayName,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isSelected ? const Color(0xFF5A67D8) : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHSNSelectionBottomSheet(BuildContext context, String currentHSN) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildHSNSelectionBottomSheet(currentHSN),
    );
  }

  Widget _buildHSNSelectionBottomSheet(String currentHSN) {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHSNBottomSheetHeader(),

                // Search Field
                _buildHSNSearchField(setModalState),

                // Content
                Expanded(child: _buildHSNList(currentHSN, setModalState)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHSNBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Search HSN',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 24, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHSNSearchField(StateSetter setModalState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextFormField(
        onChanged: (value) {
          setModalState(() {
            _hsnSearchQuery = value.toLowerCase();
          });
        },
        decoration: InputDecoration(
          hintText: 'Search by Item name',
          hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          filled: true,
          fillColor: Colors.grey[100],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildHSNList(String currentHSN, StateSetter setModalState) {
    final filteredHSNCodes = _getFilteredHSNCodes();

    if (filteredHSNCodes.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: AppText('No results found', fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: filteredHSNCodes.map((hsnData) {
          final bool isSelected = hsnData['code'] == currentHSN;
          return _buildHSNOptionItem(hsnData, isSelected, setModalState);
        }).toList(),
      ),
    );
  }

  List<Map<String, String>> _getFilteredHSNCodes() {
    if (_hsnSearchQuery.isEmpty) {
      return _hsnCodes;
    }

    return _hsnCodes.where((hsn) {
      final code = hsn['code']!.toLowerCase();
      final description = hsn['description']!.toLowerCase();
      return code.contains(_hsnSearchQuery) ||
          description.contains(_hsnSearchQuery);
    }).toList();
  }

  Widget _buildHSNOptionItem(
    Map<String, String> hsnData,
    bool isSelected,
    StateSetter setModalState,
  ) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        if (mounted) {
          context.read<EditItemBloc>().add(HSNChanged(hsnData['code']!));
          Navigator.pop(context);
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    hsnData['code']!,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isSelected
                        ? const Color(0xFF5A67D8)
                        : Colors.black87,
                  ),
                  const SizedBox(height: 4),
                  AppText(
                    hsnData['description']!,
                    fontSize: 12,
                    color: Colors.grey[600]!,
                    maxLines: 2,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Icon(
              isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[400],
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
}
