import 'package:flutter_bloc/flutter_bloc.dart';
import 'edit_item_event.dart';
import 'edit_item_state.dart';
import '../models/item.dart';

class EditItemBloc extends Bloc<EditItemEvent, EditItemState> {
  EditItemBloc() : super(const EditItemInitial()) {
    on<InitializeEditItem>(_onInitializeEditItem);
    on<ItemNameChanged>(_onItemNameChanged);
    on<ItemTypeChanged>(_onItemTypeChanged);
    on<UnitChanged>(_onUnitChanged);
    on<SalesPriceChanged>(_onSalesPriceChanged);
    on<SalesTaxTypeChanged>(_onSalesTaxTypeChanged);
    on<PurchasePriceChanged>(_onPurchasePriceChanged);
    on<PurchaseTaxTypeChanged>(_onPurchaseTaxTypeChanged);
    on<GSTTypeChanged>(_onGSTTypeChanged);
    on<HSNChanged>(_onHSNChanged);
    on<OpeningStockChanged>(_onOpeningStockChanged);
    on<AsOfDateChanged>(_onAsOfDateChanged);
    on<LowStockAlertChanged>(_onLowStockAlertChanged);
    on<LowStockQuantityChanged>(_onLowStockQuantityChanged);
    on<ImagesChanged>(_onImagesChanged);
    on<CategoryChanged>(_onCategoryChanged);
    on<ItemDescriptionChanged>(_onItemDescriptionChanged);
    on<ShowInOnlineStoreChanged>(_onShowInOnlineStoreChanged);
    on<UpdateItem>(_onUpdateItem);
    on<ValidateForm>(_onValidateForm);
  }

  Future<void> _onInitializeEditItem(
    InitializeEditItem event,
    Emitter<EditItemState> emit,
  ) async {
    emit(const EditItemLoading());

    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 300));

    final itemData = event.itemData;

    // Parse item type from string
    ItemType itemType = ItemType.product;
    if (itemData['type'] != null) {
      try {
        itemType = ItemType.values.firstWhere(
          (e) => e.name == itemData['type'],
          orElse: () => ItemType.product,
        );
      } catch (e) {
        itemType = ItemType.product;
      }
    }

    emit(
      EditItemFormState(
        itemId: itemData['id']?.toString() ?? '',
        name: itemData['name']?.toString() ?? '',
        type: itemType,
        unit: itemData['unit']?.toString() ?? 'BOX',
        salesPrice: _parseDouble(itemData['salesPrice']),
        salesTaxType: TaxType.withoutTax,
        purchasePrice: _parseDouble(itemData['purchasePrice']),
        purchaseTaxType: TaxType.withoutTax,
        gstType: GSTType.none,
        hsn: itemData['hsn']?.toString() ?? '',
        openingStock: _parseDouble(itemData['stock']),
        asOfDate: DateTime.now(),
        lowStockAlert: false,
        lowStockQuantity: 0.0,
        images: const [],
        category: itemData['category']?.toString(),
        description: itemData['description']?.toString() ?? '',
        showInOnlineStore: false,
        errors: const {},
        isValid: true,
        isSubmitting: false,
        showValidationErrors: false,
      ),
    );
  }

  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Future<void> _onItemNameChanged(
    ItemNameChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(name: event.name);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onItemTypeChanged(
    ItemTypeChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(type: event.type);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onUnitChanged(
    UnitChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(unit: event.unit);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onSalesPriceChanged(
    SalesPriceChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(salesPrice: event.price);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onSalesTaxTypeChanged(
    SalesTaxTypeChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(salesTaxType: event.taxType);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onPurchasePriceChanged(
    PurchasePriceChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(purchasePrice: event.price);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onPurchaseTaxTypeChanged(
    PurchaseTaxTypeChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        purchaseTaxType: event.taxType,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onGSTTypeChanged(
    GSTTypeChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(gstType: event.gstType);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onHSNChanged(
    HSNChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(hsn: event.hsn);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onOpeningStockChanged(
    OpeningStockChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        openingStock: event.openingStock,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onAsOfDateChanged(
    AsOfDateChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(asOfDate: event.asOfDate);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onLowStockAlertChanged(
    LowStockAlertChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        lowStockAlert: event.lowStockAlert,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onLowStockQuantityChanged(
    LowStockQuantityChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        lowStockQuantity: event.lowStockQuantity,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onImagesChanged(
    ImagesChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(images: event.images);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onCategoryChanged(
    CategoryChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(category: event.category);
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onItemDescriptionChanged(
    ItemDescriptionChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        description: event.description,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onShowInOnlineStoreChanged(
    ShowInOnlineStoreChanged event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final updatedState = currentState.copyWith(
        showInOnlineStore: event.showInOnlineStore,
      );
      emit(updatedState);
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Map<String, String> _validateForm(EditItemFormState state) {
    final errors = <String, String>{};

    // Validate item name
    if (state.name.trim().isEmpty) {
      errors['name'] = 'Item name is required';
    }

    // Validate unit
    if (state.unit.trim().isEmpty) {
      errors['unit'] = 'Unit is required';
    }

    // Validate sales price
    if (state.salesPrice < 0) {
      errors['salesPrice'] = 'Sales price cannot be negative';
    }

    // Validate purchase price
    if (state.purchasePrice < 0) {
      errors['purchasePrice'] = 'Purchase price cannot be negative';
    }

    // Validate opening stock
    if (state.openingStock < 0) {
      errors['openingStock'] = 'Opening stock cannot be negative';
    }

    // Validate low stock quantity if alert is enabled
    if (state.lowStockAlert && state.lowStockQuantity < 0) {
      errors['lowStockQuantity'] = 'Low stock quantity cannot be negative';
    }

    return errors;
  }

  Future<void> _onValidateForm(
    ValidateForm event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;
      final errors = _validateForm(currentState);
      final isValid = errors.isEmpty;

      emit(currentState.copyWith(errors: errors, isValid: isValid));
    }
  }

  Future<void> _onUpdateItem(
    UpdateItem event,
    Emitter<EditItemState> emit,
  ) async {
    if (state is EditItemFormState) {
      final currentState = state as EditItemFormState;

      // Enable validation error display and validate the form
      final stateWithValidation = currentState.copyWith(
        showValidationErrors: true,
      );
      emit(stateWithValidation);

      // Perform validation
      final errors = _validateForm(stateWithValidation);
      final isValid = errors.isEmpty;

      final validatedState = stateWithValidation.copyWith(
        errors: errors,
        isValid: isValid,
      );
      emit(validatedState);

      if (!isValid) {
        return;
      }

      emit(validatedState.copyWith(isSubmitting: true));

      try {
        // Simulate API call delay
        await Future.delayed(const Duration(milliseconds: 1500));

        final updatedItem = Item(
          id: validatedState.itemId,
          name: validatedState.name.trim(),
          type: validatedState.type,
          unit: validatedState.unit.trim(),
          salesPrice: validatedState.salesPrice,
          salesTaxType: validatedState.salesTaxType,
          purchasePrice: validatedState.purchasePrice,
          purchaseTaxType: validatedState.purchaseTaxType,
          gstType: validatedState.gstType,
          hsn: validatedState.hsn.trim(),
          openingStock: validatedState.openingStock,
          asOfDate: validatedState.asOfDate,
          lowStockAlert: validatedState.lowStockAlert,
          lowStockQuantity: validatedState.lowStockQuantity,
          images: validatedState.images,
          category: validatedState.category,
          description: validatedState.description?.trim(),
          showInOnlineStore: validatedState.showInOnlineStore,
        );

        emit(EditItemSuccess(updatedItem));
      } catch (e) {
        emit(EditItemError('Failed to update item: ${e.toString()}'));
      }
    }
  }
}
