import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class EditItemEvent extends Equatable {
  const EditItemEvent();

  @override
  List<Object?> get props => [];
}

class InitializeEditItem extends EditItemEvent {
  final Map<String, dynamic> itemData;

  const InitializeEditItem(this.itemData);

  @override
  List<Object?> get props => [itemData];
}

class ItemNameChanged extends EditItemEvent {
  final String name;

  const ItemNameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class ItemTypeChanged extends EditItemEvent {
  final ItemType type;

  const ItemTypeChanged(this.type);

  @override
  List<Object?> get props => [type];
}

class UnitChanged extends EditItemEvent {
  final String unit;

  const UnitChanged(this.unit);

  @override
  List<Object?> get props => [unit];
}

class SalesPriceChanged extends EditItemEvent {
  final double price;

  const SalesPriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class SalesTaxTypeChanged extends EditItemEvent {
  final TaxType taxType;

  const SalesTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class PurchasePriceChanged extends EditItemEvent {
  final double price;

  const PurchasePriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class PurchaseTaxTypeChanged extends EditItemEvent {
  final TaxType taxType;

  const PurchaseTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class GSTTypeChanged extends EditItemEvent {
  final GSTType gstType;

  const GSTTypeChanged(this.gstType);

  @override
  List<Object?> get props => [gstType];
}

class HSNChanged extends EditItemEvent {
  final String hsn;

  const HSNChanged(this.hsn);

  @override
  List<Object?> get props => [hsn];
}

class OpeningStockChanged extends EditItemEvent {
  final double openingStock;

  const OpeningStockChanged(this.openingStock);

  @override
  List<Object?> get props => [openingStock];
}

class AsOfDateChanged extends EditItemEvent {
  final DateTime asOfDate;

  const AsOfDateChanged(this.asOfDate);

  @override
  List<Object?> get props => [asOfDate];
}

class LowStockAlertChanged extends EditItemEvent {
  final bool lowStockAlert;

  const LowStockAlertChanged(this.lowStockAlert);

  @override
  List<Object?> get props => [lowStockAlert];
}

class LowStockQuantityChanged extends EditItemEvent {
  final double lowStockQuantity;

  const LowStockQuantityChanged(this.lowStockQuantity);

  @override
  List<Object?> get props => [lowStockQuantity];
}

class UpdateItem extends EditItemEvent {
  const UpdateItem();
}

class ValidateForm extends EditItemEvent {
  const ValidateForm();
}

class ImagesChanged extends EditItemEvent {
  final List<String> images;

  const ImagesChanged(this.images);

  @override
  List<Object?> get props => [images];
}

class CategoryChanged extends EditItemEvent {
  final String category;

  const CategoryChanged(this.category);

  @override
  List<Object?> get props => [category];
}

class ItemDescriptionChanged extends EditItemEvent {
  final String description;

  const ItemDescriptionChanged(this.description);

  @override
  List<Object?> get props => [description];
}

class ShowInOnlineStoreChanged extends EditItemEvent {
  final bool showInOnlineStore;

  const ShowInOnlineStoreChanged(this.showInOnlineStore);

  @override
  List<Object?> get props => [showInOnlineStore];
}
