import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class EditItemState extends Equatable {
  const EditItemState();

  @override
  List<Object?> get props => [];
}

class EditItemInitial extends EditItemState {
  const EditItemInitial();
}

class EditItemLoading extends EditItemState {
  const EditItemLoading();
}

class EditItemFormState extends EditItemState {
  final String itemId;
  final String name;
  final ItemType type;
  final String unit;
  final double salesPrice;
  final TaxType salesTaxType;
  final double purchasePrice;
  final TaxType purchaseTaxType;
  final GSTType gstType;
  final String hsn;
  final double openingStock;
  final DateTime asOfDate;
  final bool lowStockAlert;
  final double lowStockQuantity;
  final List<String>? images;
  final String? category;
  final String? description;
  final bool? showInOnlineStore;
  final Map<String, String> errors;
  final bool isValid;
  final bool isSubmitting;
  final bool showValidationErrors;

  const EditItemFormState({
    required this.itemId,
    required this.name,
    required this.type,
    required this.unit,
    required this.salesPrice,
    required this.salesTaxType,
    required this.purchasePrice,
    required this.purchaseTaxType,
    required this.gstType,
    required this.hsn,
    required this.openingStock,
    required this.asOfDate,
    required this.lowStockAlert,
    required this.lowStockQuantity,
    this.images,
    this.category,
    this.description,
    this.showInOnlineStore,
    required this.errors,
    required this.isValid,
    required this.isSubmitting,
    required this.showValidationErrors,
  });

  EditItemFormState copyWith({
    String? itemId,
    String? name,
    ItemType? type,
    String? unit,
    double? salesPrice,
    TaxType? salesTaxType,
    double? purchasePrice,
    TaxType? purchaseTaxType,
    GSTType? gstType,
    String? hsn,
    double? openingStock,
    DateTime? asOfDate,
    bool? lowStockAlert,
    double? lowStockQuantity,
    List<String>? images,
    String? category,
    String? description,
    bool? showInOnlineStore,
    Map<String, String>? errors,
    bool? isValid,
    bool? isSubmitting,
    bool? showValidationErrors,
  }) {
    return EditItemFormState(
      itemId: itemId ?? this.itemId,
      name: name ?? this.name,
      type: type ?? this.type,
      unit: unit ?? this.unit,
      salesPrice: salesPrice ?? this.salesPrice,
      salesTaxType: salesTaxType ?? this.salesTaxType,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseTaxType: purchaseTaxType ?? this.purchaseTaxType,
      gstType: gstType ?? this.gstType,
      hsn: hsn ?? this.hsn,
      openingStock: openingStock ?? this.openingStock,
      asOfDate: asOfDate ?? this.asOfDate,
      lowStockAlert: lowStockAlert ?? this.lowStockAlert,
      lowStockQuantity: lowStockQuantity ?? this.lowStockQuantity,
      images: images ?? this.images,
      category: category ?? this.category,
      description: description ?? this.description,
      showInOnlineStore: showInOnlineStore ?? this.showInOnlineStore,
      errors: errors ?? this.errors,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      showValidationErrors: showValidationErrors ?? this.showValidationErrors,
    );
  }

  @override
  List<Object?> get props => [
    itemId,
    name,
    type,
    unit,
    salesPrice,
    salesTaxType,
    purchasePrice,
    purchaseTaxType,
    gstType,
    hsn,
    openingStock,
    asOfDate,
    lowStockAlert,
    lowStockQuantity,
    images,
    category,
    description,
    showInOnlineStore,
    errors,
    isValid,
    isSubmitting,
    showValidationErrors,
  ];
}

class EditItemSuccess extends EditItemState {
  final Item item;

  const EditItemSuccess(this.item);

  @override
  List<Object?> get props => [item];
}

class EditItemError extends EditItemState {
  final String message;

  const EditItemError(this.message);

  @override
  List<Object?> get props => [message];
}
