import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class ReportItem extends Equatable {
  final String id;
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color iconColor;
  final String route;

  const ReportItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.iconColor,
    required this.route,
  });

  @override
  List<Object?> get props => [id, title, subtitle, icon, iconColor, route];

  factory ReportItem.fromJson(Map<String, dynamic> json) {
    return ReportItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'],
      icon: _getIconFromString(json['icon'] ?? 'description'),
      iconColor: _getColorFromString(json['iconColor'] ?? 'blue'),
      route: json['route'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'icon': _getStringFromIcon(icon),
      'iconColor': _getStringFromColor(iconColor),
      'route': route,
    };
  }

  static IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'receipt_long':
        return Icons.receipt_long;
      case 'currency_rupee':
        return Icons.currency_rupee;
      case 'menu_book':
        return Icons.menu_book;
      case 'trending_up':
        return Icons.trending_up;
      case 'person':
        return Icons.person;
      case 'inventory':
        return Icons.inventory;
      case 'balance':
        return Icons.balance;
      case 'account_balance':
        return Icons.account_balance;
      case 'groups':
        return Icons.groups;
      case 'category':
        return Icons.category;
      case 'receipt':
        return Icons.receipt;
      case 'shopping_cart':
        return Icons.shopping_cart;
      default:
        return Icons.description;
    }
  }

  static Color _getColorFromString(String colorName) {
    switch (colorName) {
      case 'purple':
        return Colors.purple;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'orange':
        return Colors.orange;
      case 'red':
        return Colors.red;
      case 'teal':
        return Colors.teal;
      case 'indigo':
        return Colors.indigo;
      case 'cyan':
        return Colors.cyan;
      default:
        return Colors.blue;
    }
  }

  static String _getStringFromIcon(IconData icon) {
    // This is a simplified mapping - in a real app you might want a more robust solution
    return 'description';
  }

  static String _getStringFromColor(Color color) {
    // This is a simplified mapping - in a real app you might want a more robust solution
    return 'blue';
  }
}
