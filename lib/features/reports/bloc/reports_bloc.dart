import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'reports_event.dart';
import 'reports_state.dart';
import '../models/report_item.dart';

class ReportsBloc extends Bloc<ReportsEvent, ReportsState> {
  ReportsBloc() : super(const ReportsInitial()) {
    on<LoadReports>(_onLoadReports);
    on<RefreshReports>(_onRefreshReports);
    on<NavigateToReport>(_onNavigateToReport);
  }

  // Mock data for reports - replace with actual API calls
  final List<ReportItem> _popularReports = [
    const ReportItem(
      id: 'bill_wise_profit',
      title: 'Bill wise profit',
      icon: Icons.receipt_long,
      iconColor: Colors.purple,
      route: '/bill-wise-profit',
    ),
    const ReportItem(
      id: 'sales_summary',
      title: 'Sales Summary',
      icon: Icons.currency_rupee,
      iconColor: Colors.blue,
      route: '/sales-summary',
    ),
    const ReportItem(
      id: 'daybook',
      title: 'Daybook',
      icon: Icons.menu_book,
      iconColor: Colors.purple,
      route: '/daybook',
    ),
    const ReportItem(
      id: 'profit_and_loss',
      title: 'Profit and Loss',
      icon: Icons.trending_up,
      iconColor: Colors.purple,
      route: '/profit-and-loss',
    ),
    const ReportItem(
      id: 'party_statement',
      title: 'Party Statement (Ledger)',
      icon: Icons.person,
      iconColor: Colors.purple,
      route: '/party-statement',
    ),
    const ReportItem(
      id: 'stock_summary',
      title: 'Stock Summary',
      subtitle: 'A summary of price & stock of all items',
      icon: Icons.inventory,
      iconColor: Colors.purple,
      route: '/stock-summary',
    ),
    const ReportItem(
      id: 'balance_sheet',
      title: 'Balance Sheet',
      icon: Icons.balance,
      iconColor: Colors.purple,
      route: '/balance-sheet',
    ),
    const ReportItem(
      id: 'cash_and_bank',
      title: 'Cash and Bank (All Payments)',
      icon: Icons.account_balance,
      iconColor: Colors.blue,
      route: '/cash-and-bank',
    ),
  ];

  final List<ReportItem> _moreReports = [
    const ReportItem(
      id: 'party_reports',
      title: 'Party Reports',
      icon: Icons.groups,
      iconColor: Colors.purple,
      route: '/party-reports',
    ),
    const ReportItem(
      id: 'item_reports',
      title: 'Item Reports',
      icon: Icons.category,
      iconColor: Colors.purple,
      route: '/item-reports',
    ),
    const ReportItem(
      id: 'gst_reports',
      title: 'GST Reports',
      icon: Icons.receipt,
      iconColor: Colors.purple,
      route: '/gst-reports',
    ),
    const ReportItem(
      id: 'transaction_reports',
      title: 'Transaction Reports',
      icon: Icons.shopping_cart,
      iconColor: Colors.blue,
      route: '/transaction-reports',
    ),
  ];

  Future<void> _onLoadReports(
    LoadReports event,
    Emitter<ReportsState> emit,
  ) async {
    emit(const ReportsLoading());

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      emit(ReportsLoaded(
        popularReports: _popularReports,
        moreReports: _moreReports,
      ));
    } catch (e) {
      emit(ReportsError('Failed to load reports: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshReports(
    RefreshReports event,
    Emitter<ReportsState> emit,
  ) async {
    // For refresh, we can directly emit loaded state or show loading briefly
    emit(const ReportsLoading());
    
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      emit(ReportsLoaded(
        popularReports: _popularReports,
        moreReports: _moreReports,
      ));
    } catch (e) {
      emit(ReportsError('Failed to refresh reports: ${e.toString()}'));
    }
  }

  Future<void> _onNavigateToReport(
    NavigateToReport event,
    Emitter<ReportsState> emit,
  ) async {
    // Handle navigation logic here
    // This could trigger navigation through a navigation service
    // For now, we'll just log the navigation request
    print('Navigating to report: ${event.reportType}');
  }
}
