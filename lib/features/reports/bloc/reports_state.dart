import 'package:equatable/equatable.dart';
import '../models/report_item.dart';

abstract class ReportsState extends Equatable {
  const ReportsState();

  @override
  List<Object?> get props => [];
}

class ReportsInitial extends ReportsState {
  const ReportsInitial();
}

class ReportsLoading extends ReportsState {
  const ReportsLoading();
}

class ReportsLoaded extends ReportsState {
  final List<ReportItem> popularReports;
  final List<ReportItem> moreReports;

  const ReportsLoaded({
    required this.popularReports,
    required this.moreReports,
  });

  @override
  List<Object?> get props => [popularReports, moreReports];
}

class ReportsError extends ReportsState {
  final String message;

  const ReportsError(this.message);

  @override
  List<Object?> get props => [message];
}
