import 'package:equatable/equatable.dart';

abstract class ReportsEvent extends Equatable {
  const ReportsEvent();

  @override
  List<Object?> get props => [];
}

class LoadReports extends ReportsEvent {
  const LoadReports();
}

class RefreshReports extends ReportsEvent {
  const RefreshReports();
}

class NavigateToReport extends ReportsEvent {
  final String reportType;

  const NavigateToReport(this.reportType);

  @override
  List<Object?> get props => [reportType];
}
