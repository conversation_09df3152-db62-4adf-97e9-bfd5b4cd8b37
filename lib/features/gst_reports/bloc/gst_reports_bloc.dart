import 'package:flutter_bloc/flutter_bloc.dart';
import 'gst_reports_event.dart';
import 'gst_reports_state.dart';

class GstReportsBloc extends Bloc<GstReportsEvent, GstReportsState> {
  GstReportsBloc() : super(const GstReportsState()) {
    on<LoadGstReports>(_onLoadGstReports);
    on<GstReportItemTapped>(_onGstReportItemTapped);
    on<RefreshGstReports>(_onRefreshGstReports);
  }

  Future<void> _onLoadGstReports(
    LoadGstReports event,
    Emitter<GstReportsState> emit,
  ) async {
    emit(state.copyWith(status: GstReportsStatus.loading));

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Sample report items matching the image design
      final reportItems = [
        const GstReportItem(
          id: 'gstr_1_sales',
          title: 'GSTR-1(sales)',
          description: null,
          type: 'gstr_1_sales',
        ),
        const GstReportItem(
          id: 'gstr_2_purchase',
          title: 'GSTR-2(purchase)',
          description: null,
          type: 'gstr_2_purchase',
        ),
        const GstReportItem(
          id: 'gstr_3b',
          title: 'GSTR-3B',
          description: null,
          type: 'gstr_3b',
        ),
        const GstReportItem(
          id: 'gst_sales_with_hsn',
          title: 'GST Sales(with HSN)',
          description: null,
          type: 'gst_sales_with_hsn',
        ),
        const GstReportItem(
          id: 'gst_purchases_with_hsn',
          title: 'GST Purchases(with HSN)',
          description: null,
          type: 'gst_purchases_with_hsn',
        ),
        const GstReportItem(
          id: 'hsn_wise_sales_summary',
          title: 'HSN Wise Sales Summary',
          description: null,
          type: 'hsn_wise_sales_summary',
        ),
        const GstReportItem(
          id: 'tds_payable',
          title: 'TDS Payable',
          description: null,
          type: 'tds_payable',
        ),
        const GstReportItem(
          id: 'tds_receivable',
          title: 'TDS Receivable',
          description: null,
          type: 'tds_receivable',
        ),
        const GstReportItem(
          id: 'tcs_payable',
          title: 'TCS Payable',
          description: null,
          type: 'tcs_payable',
        ),
        const GstReportItem(
          id: 'tcs_receivable',
          title: 'TCS Receivable',
          description: null,
          type: 'tcs_receivable',
        ),
      ];

      emit(state.copyWith(
        status: GstReportsStatus.loaded,
        reportItems: reportItems,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: GstReportsStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onGstReportItemTapped(
    GstReportItemTapped event,
    Emitter<GstReportsState> emit,
  ) {
    // Handle navigation to specific report screens
    // This will be handled in the UI layer
  }

  Future<void> _onRefreshGstReports(
    RefreshGstReports event,
    Emitter<GstReportsState> emit,
  ) async {
    add(const LoadGstReports());
  }
}
