import 'package:equatable/equatable.dart';

enum GstReportsStatus { initial, loading, loaded, error }

class GstReportItem extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String type;

  const GstReportItem({
    required this.id,
    required this.title,
    this.description,
    required this.type,
  });

  @override
  List<Object?> get props => [id, title, description, type];
}

class GstReportsState extends Equatable {
  final GstReportsStatus status;
  final List<GstReportItem> reportItems;
  final String? errorMessage;

  const GstReportsState({
    this.status = GstReportsStatus.initial,
    this.reportItems = const [],
    this.errorMessage,
  });

  GstReportsState copyWith({
    GstReportsStatus? status,
    List<GstReportItem>? reportItems,
    String? errorMessage,
  }) {
    return GstReportsState(
      status: status ?? this.status,
      reportItems: reportItems ?? this.reportItems,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, reportItems, errorMessage];
}
