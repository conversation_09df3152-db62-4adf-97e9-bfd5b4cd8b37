import 'package:equatable/equatable.dart';

abstract class GstReportsEvent extends Equatable {
  const GstReportsEvent();

  @override
  List<Object?> get props => [];
}

class LoadGstReports extends GstReportsEvent {
  const LoadGstReports();
}

class GstReportItemTapped extends GstReportsEvent {
  final String reportType;

  const GstReportItemTapped(this.reportType);

  @override
  List<Object?> get props => [reportType];
}

class RefreshGstReports extends GstReportsEvent {
  const RefreshGstReports();
}
