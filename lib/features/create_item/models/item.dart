import 'package:equatable/equatable.dart';

enum ItemType { product, service }

enum TaxType { withoutTax, withTax }

enum GSTType { none, gst5, gst12, gst18, gst28 }

class Item extends Equatable {
  final String id;
  final String name;
  final ItemType type;
  final String unit;
  final double salesPrice;
  final TaxType salesTaxType;
  final double purchasePrice;
  final TaxType purchaseTaxType;
  final GSTType gstType;
  final String hsn;
  final double openingStock;
  final DateTime asOfDate;
  final bool lowStockAlert;
  final double lowStockQuantity;
  final List<String>? images;
  final String? category;
  final String? description;
  final bool? showInOnlineStore;

  const Item({
    required this.id,
    required this.name,
    required this.type,
    required this.unit,
    required this.salesPrice,
    required this.salesTaxType,
    required this.purchasePrice,
    required this.purchaseTaxType,
    required this.gstType,
    required this.hsn,
    required this.openingStock,
    required this.asOfDate,
    required this.lowStockAlert,
    required this.lowStockQuantity,
    this.images,
    this.category,
    this.description,
    this.showInOnlineStore,
  });

  Item copyWith({
    String? id,
    String? name,
    ItemType? type,
    String? unit,
    double? salesPrice,
    TaxType? salesTaxType,
    double? purchasePrice,
    TaxType? purchaseTaxType,
    GSTType? gstType,
    String? hsn,
    double? openingStock,
    DateTime? asOfDate,
    bool? lowStockAlert,
    double? lowStockQuantity,
    List<String>? images,
    String? category,
    String? description,
    bool? showInOnlineStore,
  }) {
    return Item(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      unit: unit ?? this.unit,
      salesPrice: salesPrice ?? this.salesPrice,
      salesTaxType: salesTaxType ?? this.salesTaxType,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseTaxType: purchaseTaxType ?? this.purchaseTaxType,
      gstType: gstType ?? this.gstType,
      hsn: hsn ?? this.hsn,
      openingStock: openingStock ?? this.openingStock,
      asOfDate: asOfDate ?? this.asOfDate,
      lowStockAlert: lowStockAlert ?? this.lowStockAlert,
      lowStockQuantity: lowStockQuantity ?? this.lowStockQuantity,
      images: images ?? this.images,
      category: category ?? this.category,
      description: description ?? this.description,
      showInOnlineStore: showInOnlineStore ?? this.showInOnlineStore,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'unit': unit,
      'salesPrice': salesPrice,
      'salesTaxType': salesTaxType.name,
      'purchasePrice': purchasePrice,
      'purchaseTaxType': purchaseTaxType.name,
      'gstType': gstType.name,
      'hsn': hsn,
      'openingStock': openingStock,
      'asOfDate': asOfDate.toIso8601String(),
      'lowStockAlert': lowStockAlert,
      'lowStockQuantity': lowStockQuantity,
      'images': images,
      'category': category,
      'description': description,
      'showInOnlineStore': showInOnlineStore,
    };
  }

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['id'] as String,
      name: json['name'] as String,
      type: ItemType.values.firstWhere((e) => e.name == json['type']),
      unit: json['unit'] as String,
      salesPrice: (json['salesPrice'] as num).toDouble(),
      salesTaxType: TaxType.values.firstWhere(
        (e) => e.name == json['salesTaxType'],
      ),
      purchasePrice: (json['purchasePrice'] as num).toDouble(),
      purchaseTaxType: TaxType.values.firstWhere(
        (e) => e.name == json['purchaseTaxType'],
      ),
      gstType: GSTType.values.firstWhere((e) => e.name == json['gstType']),
      hsn: json['hsn'] as String,
      openingStock: (json['openingStock'] ?? 0).toDouble(),
      asOfDate: DateTime.parse(
        json['asOfDate'] ?? DateTime.now().toIso8601String(),
      ),
      lowStockAlert: json['lowStockAlert'] ?? false,
      lowStockQuantity: (json['lowStockQuantity'] ?? 0).toDouble(),
      images: json['images'] != null
          ? List<String>.from(json['images'] as List)
          : null,
      category: json['category'] as String?,
      description: json['description'] as String?,
      showInOnlineStore: json['showInOnlineStore'] as bool?,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    type,
    unit,
    salesPrice,
    salesTaxType,
    purchasePrice,
    purchaseTaxType,
    gstType,
    hsn,
    openingStock,
    asOfDate,
    lowStockAlert,
    lowStockQuantity,
    images,
    category,
    description,
    showInOnlineStore,
  ];
}

// Extension methods for display
extension ItemTypeExtension on ItemType {
  String get displayName {
    switch (this) {
      case ItemType.product:
        return 'Product';
      case ItemType.service:
        return 'Service';
    }
  }
}

extension TaxTypeExtension on TaxType {
  String get displayName {
    switch (this) {
      case TaxType.withoutTax:
        return 'Without Tax';
      case TaxType.withTax:
        return 'With Tax';
    }
  }
}

extension GSTTypeExtension on GSTType {
  String get displayName {
    switch (this) {
      case GSTType.none:
        return 'None';
      case GSTType.gst5:
        return '5% GST';
      case GSTType.gst12:
        return '12% GST';
      case GSTType.gst18:
        return '18% GST';
      case GSTType.gst28:
        return '28% GST';
    }
  }
}
