import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/create_item_bloc.dart';
import '../bloc/create_item_event.dart';
import '../bloc/create_item_state.dart';

class OtherTab extends StatefulWidget {
  final CreateItemFormState state;
  final TextEditingController itemDescriptionController;

  const OtherTab({
    super.key,
    required this.state,
    required this.itemDescriptionController,
  });

  @override
  State<OtherTab> createState() => _OtherTabState();
}

class _OtherTabState extends State<OtherTab> {
  final ImagePicker _imagePicker = ImagePicker();
  List<XFile> _selectedImages = [];
  String _selectedCategory = 'Select Category';
  bool _showInOnlineStore = false;

  // Sample categories for demonstration
  static const List<String> _categories = [
    'Select Category',
    'Electronics',
    'Clothing',
    'Food & Beverages',
    'Home & Garden',
    'Sports & Outdoors',
    'Books & Media',
    'Health & Beauty',
    'Automotive',
    'Office Supplies',
    'Toys & Games',
  ];

  @override
  void initState() {
    super.initState();
    _showInOnlineStore = widget.state.showInOnlineStore ?? false;
    _selectedCategory = widget.state.category ?? 'Select Category';
    // Initialize with existing images if any
    if (widget.state.images != null && widget.state.images!.isNotEmpty) {
      _selectedImages = widget.state.images!
          .map((path) => XFile(path))
          .toList();
    }
    // Initialize description controller
    widget.itemDescriptionController.text = widget.state.description ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: AnimationLimiter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 20.0,
                child: FadeInAnimation(child: widget),
              ),
              children: [
                _buildImageSection(),
                const SizedBox(height: 24),
                _buildCategorySection(),
                const SizedBox(height: 24),
                _buildCustomFieldsSection(),
                const SizedBox(height: 24),
                _buildDescriptionSection(),
                const SizedBox(height: 24),
                _buildOnlineStoreSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Product Images',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: _selectedImages.isEmpty ? 120 : null,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: _selectedImages.isEmpty
              ? _buildAddImageButton()
              : _buildImageGrid(),
        ),
      ],
    );
  }

  Widget _buildAddImageButton() {
    return InkWell(
      onTap: _showImageSourceBottomSheet,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        height: 120,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.camera_alt_outlined,
                size: 32,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 10),
            AppText(
              'Add Image',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600]!,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: _selectedImages.length + 1,
            itemBuilder: (context, index) {
              if (index == _selectedImages.length) {
                return _buildAddMoreButton();
              }
              return _buildImageItem(_selectedImages[index], index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddMoreButton() {
    return InkWell(
      onTap: _showImageSourceBottomSheet,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[300]!,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add, size: 24, color: Colors.grey[600]),
            const SizedBox(height: 4),
            AppText(
              'Add',
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600]!,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(XFile image, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(image.path),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Item Category',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: _showCategorySelectionBottomSheet,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  _selectedCategory,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: _selectedCategory == 'Select Category'
                      ? Colors.grey[500]!
                      : Colors.black87,
                ),
                Icon(Icons.keyboard_arrow_down, color: Colors.grey[600]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Custom Fields',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: _showCustomFieldsBottomSheet,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF5A67D8)),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.add, color: Color(0xFF5A67D8), size: 20),
                SizedBox(width: 8),
                AppText(
                  'Add Fields to Item',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF5A67D8),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Item Description',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextFormField(
            controller: widget.itemDescriptionController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: 'Ex: 100% Real Mixed Fruit Jam',
              hintStyle: TextStyle(
                color: Colors.grey[500],
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Colors.black87,
            ),
            onChanged: (value) {
              context.read<CreateItemBloc>().add(ItemDescriptionChanged(value));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOnlineStoreSection() {
    return Row(
      children: [
        Switch.adaptive(
          value: _showInOnlineStore,
          onChanged: (value) {
            HapticFeedback.lightImpact();
            setState(() {
              _showInOnlineStore = value;
            });
            context.read<CreateItemBloc>().add(ShowInOnlineStoreChanged(value));
          },
          activeColor: const Color(0xFF5A67D8),
          activeTrackColor: const Color(0xFF5A67D8).withValues(alpha: 0.3),
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: AppText(
            'Show in Online Store',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  // Image management methods
  void _showImageSourceBottomSheet() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildImageSourceBottomSheet(),
    );
  }

  Widget _buildImageSourceBottomSheet() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Header
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: AppText(
                'Select Image Source',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 24),

            // Options
            _buildImageSourceOption(
              icon: Icons.camera_alt,
              title: 'Camera',
              subtitle: 'Take a photo',
              onTap: () => _pickImageFromCamera(),
            ),
            _buildImageSourceOption(
              icon: Icons.photo_library,
              title: 'Gallery',
              subtitle: 'Choose from gallery',
              onTap: () => _pickImageFromGallery(),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: const Color(0xFF5A67D8), size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    title,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  const SizedBox(height: 2),
                  AppText(
                    subtitle,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600]!,
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  // Image picker methods
  Future<void> _pickImageFromCamera() async {
    Navigator.pop(context); // Close bottom sheet
    HapticFeedback.mediumImpact();

    try {
      // Check and request camera permission
      final hasPermission = await _requestCameraPermission();
      if (!hasPermission) {
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
        _updateImagesInBloc();
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorDialog('Failed to capture image: ${e.toString()}');
    }
  }

  Future<void> _pickImageFromGallery() async {
    Navigator.pop(context); // Close bottom sheet
    HapticFeedback.mediumImpact();

    try {
      // Check and request photo library permission
      final hasPermission = await _requestPhotosPermission();
      if (!hasPermission) {
        return;
      }

      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
        });
        _updateImagesInBloc();
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorDialog('Failed to select images: ${e.toString()}');
    }
  }

  // Permission handling methods
  Future<bool> _requestCameraPermission() async {
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      // Show explanation dialog first
      final shouldRequest = await _showPermissionExplanationDialog(
        'Camera',
        'This app needs camera access to take photos for your items.',
      );

      if (!shouldRequest) {
        return false;
      }

      final result = await Permission.camera.request();
      if (result.isGranted) {
        return true;
      } else if (result.isDenied) {
        _showPermissionDeniedDialog('Camera', false);
        return false;
      } else if (result.isPermanentlyDenied) {
        _showPermissionDeniedDialog('Camera', true);
        return false;
      }
    }

    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog('Camera', true);
      return false;
    }

    return false;
  }

  Future<bool> _requestPhotosPermission() async {
    // For iOS, we need to handle different photo permissions
    Permission photoPermission = Permission.photos;

    // Check for full photos permission first
    final photosStatus = await Permission.photos.status;
    if (photosStatus.isGranted) {
      return true;
    }

    // On iOS 14+, check for limited photos permission
    if (Platform.isIOS) {
      final limitedStatus = await Permission.photosAddOnly.status;
      if (limitedStatus.isGranted) {
        return true; // Limited access is still usable
      }
    }

    final status = photosStatus;

    if (status.isDenied) {
      // Show explanation dialog first
      final shouldRequest = await _showPermissionExplanationDialog(
        'Photos',
        'This app needs photo library access to select images for your items.',
      );

      if (!shouldRequest) {
        return false;
      }

      final result = await photoPermission.request();
      if (result.isGranted) {
        return true;
      } else if (result.isDenied) {
        _showPermissionDeniedDialog('Photos', false);
        return false;
      } else if (result.isPermanentlyDenied) {
        _showPermissionDeniedDialog('Photos', true);
        return false;
      }
    }

    if (status.isPermanentlyDenied) {
      _showPermissionDeniedDialog('Photos', true);
      return false;
    }

    return false;
  }

  Future<bool> _showPermissionExplanationDialog(
    String permissionType,
    String explanation,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog.adaptive(
        title: Text(
          '$permissionType Permission',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        content: Text(
          explanation,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: Colors.black54,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              Platform.isIOS ? 'Cancel' : 'CANCEL',
              style: TextStyle(
                fontSize: Platform.isIOS ? 16 : 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              Platform.isIOS ? 'Continue' : 'CONTINUE',
              style: TextStyle(
                fontSize: Platform.isIOS ? 16 : 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF5A67D8),
              ),
            ),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  void _removeImage(int index) {
    HapticFeedback.lightImpact();
    setState(() {
      _selectedImages.removeAt(index);
    });
    _updateImagesInBloc();
  }

  void _updateImagesInBloc() {
    final imagePaths = _selectedImages.map((image) => image.path).toList();
    context.read<CreateItemBloc>().add(ImagesChanged(imagePaths));
  }

  // Category selection
  void _showCategorySelectionBottomSheet() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCategorySelectionBottomSheet(),
    );
  }

  Widget _buildCategorySelectionBottomSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Drag handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Select Category',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Category list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: _categories.length - 1, // Exclude 'Select Category'
                itemBuilder: (context, index) {
                  final category = _categories[index + 1];
                  final isSelected = _selectedCategory == category;

                  return _buildCategoryItem(category, isSelected);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(String category, bool isSelected) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedCategory = category;
        });
        context.read<CreateItemBloc>().add(CategoryChanged(category));
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
              : Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: AppText(
                category,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isSelected ? const Color(0xFF5A67D8) : Colors.black87,
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Color(0xFF5A67D8),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  // Custom fields bottom sheet
  void _showCustomFieldsBottomSheet() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCustomFieldsBottomSheet(),
    );
  }

  Widget _buildCustomFieldsBottomSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Drag handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Custom Fields',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        children: [
                          Icon(
                            Icons.add_box_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          AppText(
                            'No Custom Fields',
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600]!,
                          ),
                          const SizedBox(height: 8),
                          AppText(
                            'Add custom fields to capture additional information for your items',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey[500]!,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Permission and error dialogs
  void _showPermissionDeniedDialog(
    String permissionType,
    bool isPermanentlyDenied,
  ) {
    final String title = '$permissionType Permission Required';
    final String message = isPermanentlyDenied
        ? 'Please enable $permissionType permission in Settings to add images to your item.'
        : 'This app needs $permissionType permission to add images to your item.';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog.adaptive(
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        content: Text(
          message,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: Colors.black54,
          ),
        ),
        actions: _buildDialogActions(isPermanentlyDenied),
      ),
    );
  }

  List<Widget> _buildDialogActions(bool isPermanentlyDenied) {
    if (Platform.isIOS) {
      return [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'Cancel',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
        ),
        if (isPermanentlyDenied)
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text(
              'Settings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          )
        else
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Retry permission request
              _retryPermissionRequest();
            },
            child: const Text(
              'Allow',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          ),
      ];
    } else {
      // Android Material Design
      return [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'CANCEL',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
        ),
        if (isPermanentlyDenied)
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text(
              'SETTINGS',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          )
        else
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Retry permission request
              _retryPermissionRequest();
            },
            child: const Text(
              'ALLOW',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          ),
      ];
    }
  }

  void _retryPermissionRequest() {
    // This will be called when user taps "Allow" on the dialog
    // The permission request will happen when they try the action again
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        title: const Text(
          'Error',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
        content: Text(
          message,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: Colors.black54,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              Platform.isIOS ? 'OK' : 'OK',
              style: TextStyle(
                fontSize: Platform.isIOS ? 16 : 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF5A67D8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
