import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class CreateItemState extends Equatable {
  const CreateItemState();

  @override
  List<Object?> get props => [];
}

class CreateItemInitial extends CreateItemState {
  const CreateItemInitial();
}

class CreateItemLoading extends CreateItemState {
  const CreateItemLoading();
}

class CreateItemFormState extends CreateItemState {
  final String name;
  final ItemType type;
  final String unit;
  final double salesPrice;
  final TaxType salesTaxType;
  final double purchasePrice;
  final TaxType purchaseTaxType;
  final GSTType gstType;
  final String hsn;
  final double openingStock;
  final DateTime asOfDate;
  final bool lowStockAlert;
  final double lowStockQuantity;
  final List<String>? images;
  final String? category;
  final String? description;
  final bool? showInOnlineStore;
  final Map<String, String> errors;
  final bool isValid;
  final bool isSubmitting;
  final bool showValidationErrors;

  const CreateItemFormState({
    required this.name,
    required this.type,
    required this.unit,
    required this.salesPrice,
    required this.salesTaxType,
    required this.purchasePrice,
    required this.purchaseTaxType,
    required this.gstType,
    required this.hsn,
    required this.openingStock,
    required this.asOfDate,
    required this.lowStockAlert,
    required this.lowStockQuantity,
    this.images,
    this.category,
    this.description,
    this.showInOnlineStore,
    required this.errors,
    required this.isValid,
    required this.isSubmitting,
    required this.showValidationErrors,
  });

  CreateItemFormState copyWith({
    String? name,
    ItemType? type,
    String? unit,
    double? salesPrice,
    TaxType? salesTaxType,
    double? purchasePrice,
    TaxType? purchaseTaxType,
    GSTType? gstType,
    String? hsn,
    double? openingStock,
    DateTime? asOfDate,
    bool? lowStockAlert,
    double? lowStockQuantity,
    List<String>? images,
    String? category,
    String? description,
    bool? showInOnlineStore,
    Map<String, String>? errors,
    bool? isValid,
    bool? isSubmitting,
    bool? showValidationErrors,
  }) {
    return CreateItemFormState(
      name: name ?? this.name,
      type: type ?? this.type,
      unit: unit ?? this.unit,
      salesPrice: salesPrice ?? this.salesPrice,
      salesTaxType: salesTaxType ?? this.salesTaxType,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseTaxType: purchaseTaxType ?? this.purchaseTaxType,
      gstType: gstType ?? this.gstType,
      hsn: hsn ?? this.hsn,
      openingStock: openingStock ?? this.openingStock,
      asOfDate: asOfDate ?? this.asOfDate,
      lowStockAlert: lowStockAlert ?? this.lowStockAlert,
      lowStockQuantity: lowStockQuantity ?? this.lowStockQuantity,
      images: images ?? this.images,
      category: category ?? this.category,
      description: description ?? this.description,
      showInOnlineStore: showInOnlineStore ?? this.showInOnlineStore,
      errors: errors ?? this.errors,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      showValidationErrors: showValidationErrors ?? this.showValidationErrors,
    );
  }

  @override
  List<Object?> get props => [
    name,
    type,
    unit,
    salesPrice,
    salesTaxType,
    purchasePrice,
    purchaseTaxType,
    gstType,
    hsn,
    openingStock,
    asOfDate,
    lowStockAlert,
    lowStockQuantity,
    images,
    category,
    description,
    showInOnlineStore,
    errors,
    isValid,
    isSubmitting,
    showValidationErrors,
  ];
}

class CreateItemSuccess extends CreateItemState {
  final Item item;

  const CreateItemSuccess(this.item);

  @override
  List<Object?> get props => [item];
}

class CreateItemError extends CreateItemState {
  final String message;

  const CreateItemError(this.message);

  @override
  List<Object?> get props => [message];
}
