import 'package:flutter_bloc/flutter_bloc.dart';
import 'create_item_event.dart';
import 'create_item_state.dart';
import '../models/item.dart';

class CreateItemBloc extends Bloc<CreateItemEvent, CreateItemState> {
  CreateItemBloc() : super(const CreateItemInitial()) {
    on<InitializeCreateItem>(_onInitializeCreateItem);
    on<ItemNameChanged>(_onItemNameChanged);
    on<ItemTypeChanged>(_onItemTypeChanged);
    on<UnitChanged>(_onUnitChanged);
    on<SalesPriceChanged>(_onSalesPriceChanged);
    on<SalesTaxTypeChanged>(_onSalesTaxTypeChanged);
    on<PurchasePriceChanged>(_onPurchasePriceChanged);
    on<PurchaseTaxTypeChanged>(_onPurchaseTaxTypeChanged);
    on<GSTTypeChanged>(_onGSTTypeChanged);
    on<HSNChanged>(_onHSNChanged);
    on<OpeningStockChanged>(_onOpeningStockChanged);
    on<AsOfDateChanged>(_onAsOfDateChanged);
    on<LowStockAlertChanged>(_onLowStockAlertChanged);
    on<LowStockQuantityChanged>(_onLowStockQuantityChanged);
    on<ImagesChanged>(_onImagesChanged);
    on<CategoryChanged>(_onCategoryChanged);
    on<ItemDescriptionChanged>(_onItemDescriptionChanged);
    on<ShowInOnlineStoreChanged>(_onShowInOnlineStoreChanged);
    on<SaveItem>(_onSaveItem);
    on<ValidateForm>(_onValidateForm);
  }

  Future<void> _onInitializeCreateItem(
    InitializeCreateItem event,
    Emitter<CreateItemState> emit,
  ) async {
    emit(const CreateItemLoading());

    // Simulate loading delay
    await Future.delayed(const Duration(milliseconds: 300));

    emit(
      CreateItemFormState(
        name: '',
        type: ItemType.product,
        unit: 'BOX',
        salesPrice: 0.0,
        salesTaxType: TaxType.withoutTax,
        purchasePrice: 0.0,
        purchaseTaxType: TaxType.withoutTax,
        gstType: GSTType.none,
        hsn: '',
        openingStock: 0.0,
        asOfDate: DateTime.now(),
        lowStockAlert: false,
        lowStockQuantity: 0.0,
        images: const [],
        category: null,
        description: '',
        showInOnlineStore: false,
        errors: const {},
        isValid: false,
        isSubmitting: false,
        showValidationErrors: false,
      ),
    );
  }

  Future<void> _onItemNameChanged(
    ItemNameChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(name: event.name);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onItemTypeChanged(
    ItemTypeChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(type: event.type);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onUnitChanged(
    UnitChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(unit: event.unit);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onSalesPriceChanged(
    SalesPriceChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(salesPrice: event.price);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onSalesTaxTypeChanged(
    SalesTaxTypeChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(salesTaxType: event.taxType);
      emit(updatedState);
    }
  }

  Future<void> _onPurchasePriceChanged(
    PurchasePriceChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(purchasePrice: event.price);
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onPurchaseTaxTypeChanged(
    PurchaseTaxTypeChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        purchaseTaxType: event.taxType,
      );
      emit(updatedState);
    }
  }

  Future<void> _onGSTTypeChanged(
    GSTTypeChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(gstType: event.gstType);
      emit(updatedState);
    }
  }

  Future<void> _onHSNChanged(
    HSNChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(hsn: event.hsn);
      emit(updatedState);
    }
  }

  Future<void> _onOpeningStockChanged(
    OpeningStockChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        openingStock: event.openingStock,
      );
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onAsOfDateChanged(
    AsOfDateChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(asOfDate: event.asOfDate);
      emit(updatedState);
    }
  }

  Future<void> _onLowStockAlertChanged(
    LowStockAlertChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        lowStockAlert: event.lowStockAlert,
      );
      emit(updatedState);
    }
  }

  Future<void> _onLowStockQuantityChanged(
    LowStockQuantityChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        lowStockQuantity: event.lowStockQuantity,
      );
      emit(updatedState);
      // Only validate if we're already showing validation errors
      if (currentState.showValidationErrors) {
        add(const ValidateForm());
      }
    }
  }

  Future<void> _onImagesChanged(
    ImagesChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(images: event.images);
      emit(updatedState);
    }
  }

  Future<void> _onCategoryChanged(
    CategoryChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(category: event.category);
      emit(updatedState);
    }
  }

  Future<void> _onItemDescriptionChanged(
    ItemDescriptionChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        description: event.description,
      );
      emit(updatedState);
    }
  }

  Future<void> _onShowInOnlineStoreChanged(
    ShowInOnlineStoreChanged event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final updatedState = currentState.copyWith(
        showInOnlineStore: event.showInOnlineStore,
      );
      emit(updatedState);
    }
  }

  Map<String, String> _validateForm(CreateItemFormState state) {
    final errors = <String, String>{};

    // Validate item name
    if (state.name.trim().isEmpty) {
      errors['name'] = 'Item name is required';
    }

    // Validate unit
    if (state.unit.trim().isEmpty) {
      errors['unit'] = 'Unit is required';
    }

    // Validate sales price
    if (state.salesPrice <= 0) {
      errors['salesPrice'] = 'Sales price must be greater than 0';
    }

    // Validate purchase price
    if (state.purchasePrice < 0) {
      errors['purchasePrice'] = 'Purchase price cannot be negative';
    }

    // Validate opening stock
    if (state.openingStock < 0) {
      errors['openingStock'] = 'Opening stock cannot be negative';
    }

    // Validate low stock quantity if alert is enabled
    if (state.lowStockAlert && state.lowStockQuantity < 0) {
      errors['lowStockQuantity'] = 'Low stock quantity cannot be negative';
    }

    return errors;
  }

  Future<void> _onValidateForm(
    ValidateForm event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;
      final errors = _validateForm(currentState);
      final isValid = errors.isEmpty;

      emit(currentState.copyWith(errors: errors, isValid: isValid));
    }
  }

  Future<void> _onSaveItem(
    SaveItem event,
    Emitter<CreateItemState> emit,
  ) async {
    if (state is CreateItemFormState) {
      final currentState = state as CreateItemFormState;

      // Enable validation error display and validate the form
      final stateWithValidation = currentState.copyWith(
        showValidationErrors: true,
      );
      emit(stateWithValidation);

      // Perform validation
      final errors = _validateForm(stateWithValidation);
      final isValid = errors.isEmpty;

      final validatedState = stateWithValidation.copyWith(
        errors: errors,
        isValid: isValid,
      );
      emit(validatedState);

      if (!isValid) {
        return;
      }

      emit(validatedState.copyWith(isSubmitting: true));

      try {
        // Simulate API call delay
        await Future.delayed(const Duration(milliseconds: 1500));

        final item = Item(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: validatedState.name.trim(),
          type: validatedState.type,
          unit: validatedState.unit.trim(),
          salesPrice: validatedState.salesPrice,
          salesTaxType: validatedState.salesTaxType,
          purchasePrice: validatedState.purchasePrice,
          purchaseTaxType: validatedState.purchaseTaxType,
          gstType: validatedState.gstType,
          hsn: validatedState.hsn.trim(),
          openingStock: validatedState.openingStock,
          asOfDate: validatedState.asOfDate,
          lowStockAlert: validatedState.lowStockAlert,
          lowStockQuantity: validatedState.lowStockQuantity,
          images: validatedState.images,
          category: validatedState.category,
          description: validatedState.description?.trim(),
          showInOnlineStore: validatedState.showInOnlineStore,
        );

        emit(CreateItemSuccess(item));
      } catch (e) {
        emit(CreateItemError('Failed to create item: ${e.toString()}'));
      }
    }
  }
}
