import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'account_settings_event.dart';
import 'account_settings_state.dart';

class AccountSettingsBloc
    extends Bloc<AccountSettingsEvent, AccountSettingsState> {
  AccountSettingsBloc() : super(const AccountSettingsInitial()) {
    on<LoadAccountSettings>(_onLoadAccountSettings);
    on<UpdateName>(_onUpdateName);
    on<ToggleAppLock>(_onToggleAppLock);
    on<NavigateToReferralCode>(_onNavigateToReferralCode);
    on<NavigateToLanguage>(_onNavigateToLanguage);
    on<NavigateToDataBackup>(_onNavigateToDataBackup);
    on<LogOut>(_onLogOut);
    on<SaveAccountSettings>(_onSaveAccountSettings);
  }

  Future<void> _onLoadAccountSettings(
    LoadAccountSettings event,
    Emitter<AccountSettingsState> emit,
  ) async {
    emit(const AccountSettingsLoading());

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Load user data (in a real app, this would come from a repository/service)
      const name = 'Your Name';
      const mobileNumber = '**********';
      const appLockEnabled = false;
      const dataBackupEnabled = true;
      const lastBackupDate = 'Thu, 17 Jul 2025 11:37 am';
      const referralCode = 'REF123456';
      const selectedLanguage = 'English';

      emit(
        const AccountSettingsLoaded(
          name: name,
          mobileNumber: mobileNumber,
          appLockEnabled: appLockEnabled,
          dataBackupEnabled: dataBackupEnabled,
          lastBackupDate: lastBackupDate,
          referralCode: referralCode,
          selectedLanguage: selectedLanguage,
        ),
      );
    } catch (e) {
      emit(
        AccountSettingsError(
          'Failed to load account settings: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateName(
    UpdateName event,
    Emitter<AccountSettingsState> emit,
  ) async {
    if (state is AccountSettingsLoaded) {
      final currentState = state as AccountSettingsLoaded;
      emit(currentState.copyWith(name: event.name));
    }
  }

  Future<void> _onToggleAppLock(
    ToggleAppLock event,
    Emitter<AccountSettingsState> emit,
  ) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();

    if (state is AccountSettingsLoaded) {
      final currentState = state as AccountSettingsLoaded;
      emit(currentState.copyWith(appLockEnabled: event.isEnabled));
    }
  }

  Future<void> _onNavigateToReferralCode(
    NavigateToReferralCode event,
    Emitter<AccountSettingsState> emit,
  ) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    // Navigation logic would be handled in the UI layer
    // TODO: Implement referral code screen navigation
  }

  Future<void> _onNavigateToLanguage(
    NavigateToLanguage event,
    Emitter<AccountSettingsState> emit,
  ) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    // Navigation logic would be handled in the UI layer
    // TODO: Implement language settings screen navigation
  }

  Future<void> _onNavigateToDataBackup(
    NavigateToDataBackup event,
    Emitter<AccountSettingsState> emit,
  ) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    // Navigation logic would be handled in the UI layer
    // TODO: Implement data backup settings screen navigation
  }

  Future<void> _onLogOut(
    LogOut event,
    Emitter<AccountSettingsState> emit,
  ) async {
    // Add haptic feedback
    HapticFeedback.lightImpact();
    // TODO: Implement logout logic (clear user data, navigate to login screen, etc.)
  }

  Future<void> _onSaveAccountSettings(
    SaveAccountSettings event,
    Emitter<AccountSettingsState> emit,
  ) async {
    if (state is AccountSettingsLoaded) {
      emit(const AccountSettingsSaving());

      try {
        // Simulate save delay
        await Future.delayed(const Duration(milliseconds: 500));

        // Save logic would go here
        emit(const AccountSettingsSaved());

        // Return to loaded state
        add(const LoadAccountSettings());
      } catch (e) {
        emit(AccountSettingsError('Failed to save settings: ${e.toString()}'));
      }
    }
  }
}
