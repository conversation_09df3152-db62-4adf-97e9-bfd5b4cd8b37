import 'package:equatable/equatable.dart';

abstract class AccountSettingsEvent extends Equatable {
  const AccountSettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadAccountSettings extends AccountSettingsEvent {
  const LoadAccountSettings();
}

class UpdateName extends AccountSettingsEvent {
  final String name;

  const UpdateName(this.name);

  @override
  List<Object?> get props => [name];
}

class ToggleAppLock extends AccountSettingsEvent {
  final bool isEnabled;

  const ToggleAppLock(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class NavigateToReferralCode extends AccountSettingsEvent {
  const NavigateToReferralCode();
}

class NavigateToLanguage extends AccountSettingsEvent {
  const NavigateToLanguage();
}

class NavigateToDataBackup extends AccountSettingsEvent {
  const NavigateToDataBackup();
}

class LogOut extends AccountSettingsEvent {
  const LogOut();
}

class SaveAccountSettings extends AccountSettingsEvent {
  const SaveAccountSettings();
}
