import 'package:equatable/equatable.dart';

abstract class AccountSettingsState extends Equatable {
  const AccountSettingsState();

  @override
  List<Object?> get props => [];
}

class AccountSettingsInitial extends AccountSettingsState {
  const AccountSettingsInitial();
}

class AccountSettingsLoading extends AccountSettingsState {
  const AccountSettingsLoading();
}

class AccountSettingsLoaded extends AccountSettingsState {
  final String name;
  final String mobileNumber;
  final bool appLockEnabled;
  final bool dataBackupEnabled;
  final String lastBackupDate;
  final String referralCode;
  final String selectedLanguage;

  const AccountSettingsLoaded({
    required this.name,
    required this.mobileNumber,
    required this.appLockEnabled,
    required this.dataBackupEnabled,
    required this.lastBackupDate,
    required this.referralCode,
    required this.selectedLanguage,
  });

  AccountSettingsLoaded copyWith({
    String? name,
    String? mobileNumber,
    bool? appLockEnabled,
    bool? dataBackupEnabled,
    String? lastBackupDate,
    String? referralCode,
    String? selectedLanguage,
  }) {
    return AccountSettingsLoaded(
      name: name ?? this.name,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      appLockEnabled: appLockEnabled ?? this.appLockEnabled,
      dataBackupEnabled: dataBackupEnabled ?? this.dataBackupEnabled,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      referralCode: referralCode ?? this.referralCode,
      selectedLanguage: selectedLanguage ?? this.selectedLanguage,
    );
  }

  @override
  List<Object?> get props => [
        name,
        mobileNumber,
        appLockEnabled,
        dataBackupEnabled,
        lastBackupDate,
        referralCode,
        selectedLanguage,
      ];
}

class AccountSettingsError extends AccountSettingsState {
  final String message;

  const AccountSettingsError(this.message);

  @override
  List<Object?> get props => [message];
}

class AccountSettingsSaving extends AccountSettingsState {
  const AccountSettingsSaving();
}

class AccountSettingsSaved extends AccountSettingsState {
  const AccountSettingsSaved();
}
