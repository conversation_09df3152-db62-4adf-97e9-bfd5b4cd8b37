import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/transaction_details_bloc.dart';
import '../bloc/transaction_details_event.dart';
import '../bloc/transaction_details_state.dart';

class DateFilterBottomSheet extends StatefulWidget {
  const DateFilterBottomSheet({super.key});

  @override
  State<DateFilterBottomSheet> createState() => _DateFilterBottomSheetState();
}

class _DateFilterBottomSheetState extends State<DateFilterBottomSheet>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    );
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _slideAnimation.value) * 300),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(children: [_buildHeader(), _buildDateOptions()]),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const AppText(
            'Select Date',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).pop();
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 20, color: Colors.black54),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateOptions() {
    return BlocBuilder<TransactionDetailsBloc, TransactionDetailsState>(
      builder: (context, state) {
        final dateOptions = [
          {'title': 'Today', 'range': _formatDateRange(_getTodayRange())},
          {
            'title': 'Yesterday',
            'range': _formatDateRange(_getYesterdayRange()),
          },
          {
            'title': 'Last 7 days',
            'range': _formatDateRange(_getLast7DaysRange()),
          },
          {
            'title': 'Last 30 days',
            'range': _formatDateRange(_getLast30DaysRange()),
          },
          {
            'title': 'This month',
            'range': _formatDateRange(_getThisMonthRange()),
          },
          {
            'title': 'Last month',
            'range': _formatDateRange(_getLastMonthRange()),
          },
          {
            'title': 'This quarter',
            'range': _formatDateRange(_getThisQuarterRange()),
          },
          {
            'title': 'Last quarter',
            'range': _formatDateRange(_getLastQuarterRange()),
          },
          {
            'title': 'This year',
            'range': _formatDateRange(_getThisYearRange()),
          },
          {
            'title': 'Last year',
            'range': _formatDateRange(_getLastYearRange()),
          },
          {'title': 'Custom', 'range': ''},
        ];

        return Expanded(
          child: AnimationLimiter(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: dateOptions.length,
              itemBuilder: (context, index) {
                final option = dateOptions[index];
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 300),
                  child: SlideAnimation(
                    verticalOffset: 30.0,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildDateOption(
                        option['title'] as String,
                        option['range'] as String,
                        state,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateOption(
    String title,
    String dateRange,
    TransactionDetailsState state,
  ) {
    final isSelected = _isDateRangeSelected(title, state);
    final isCustom = title == 'Custom';

    return GestureDetector(
      onTap: () async {
        HapticFeedback.lightImpact();
        if (isCustom) {
          Navigator.of(context).pop();
          await _showCustomDatePicker();
        } else {
          final dateRange = _getDateRangeForTitle(title);
          if (dateRange != null) {
            context.read<TransactionDetailsBloc>().add(
              ChangeDateRange(
                startDate: dateRange.start,
                endDate: dateRange.end,
              ),
            );
          }
          Navigator.of(context).pop();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey, width: 0.1)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    title,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  if (dateRange.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    AppText(dateRange, fontSize: 14, color: Colors.grey[600]),
                  ],
                ],
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                  width: 2,
                ),
                color: isSelected
                    ? const Color(0xFF5A67D8)
                    : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 14)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  bool _isDateRangeSelected(String title, TransactionDetailsState state) {
    final dateRange = _getDateRangeForTitle(title);
    if (dateRange == null) return false;

    final startDateOnly = DateTime(
      state.startDate.year,
      state.startDate.month,
      state.startDate.day,
    );
    final endDateOnly = DateTime(
      state.endDate.year,
      state.endDate.month,
      state.endDate.day,
    );
    final optionStartDateOnly = DateTime(
      dateRange.start.year,
      dateRange.start.month,
      dateRange.start.day,
    );
    final optionEndDateOnly = DateTime(
      dateRange.end.year,
      dateRange.end.month,
      dateRange.end.day,
    );

    return startDateOnly == optionStartDateOnly &&
        endDateOnly == optionEndDateOnly;
  }

  DateTimeRange? _getDateRangeForTitle(String title) {
    switch (title) {
      case 'Today':
        return _getTodayRange();
      case 'Yesterday':
        return _getYesterdayRange();
      case 'Last 7 days':
        return _getLast7DaysRange();
      case 'Last 30 days':
        return _getLast30DaysRange();
      case 'This month':
        return _getThisMonthRange();
      case 'Last month':
        return _getLastMonthRange();
      case 'This quarter':
        return _getThisQuarterRange();
      case 'Last quarter':
        return _getLastQuarterRange();
      case 'This year':
        return _getThisYearRange();
      case 'Last year':
        return _getLastYearRange();
      default:
        return null;
    }
  }

  DateTimeRange _getTodayRange() {
    final now = DateTime.now();
    return DateTimeRange(start: now, end: now);
  }

  DateTimeRange _getYesterdayRange() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return DateTimeRange(start: yesterday, end: yesterday);
  }

  DateTimeRange _getLast7DaysRange() {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 6));
    return DateTimeRange(start: weekAgo, end: now);
  }

  DateTimeRange _getLast30DaysRange() {
    final now = DateTime.now();
    final monthAgo = now.subtract(const Duration(days: 29));
    return DateTimeRange(start: monthAgo, end: now);
  }

  DateTimeRange _getThisMonthRange() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    return DateTimeRange(start: firstDayOfMonth, end: lastDayOfMonth);
  }

  DateTimeRange _getLastMonthRange() {
    final now = DateTime.now();
    final firstDayOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final lastDayOfLastMonth = DateTime(now.year, now.month, 0);
    return DateTimeRange(start: firstDayOfLastMonth, end: lastDayOfLastMonth);
  }

  DateTimeRange _getThisQuarterRange() {
    final now = DateTime.now();
    final quarter = ((now.month - 1) ~/ 3) + 1;
    final firstMonthOfQuarter = (quarter - 1) * 3 + 1;
    final firstDayOfQuarter = DateTime(now.year, firstMonthOfQuarter, 1);
    final lastDayOfQuarter = DateTime(now.year, firstMonthOfQuarter + 3, 0);
    return DateTimeRange(start: firstDayOfQuarter, end: lastDayOfQuarter);
  }

  DateTimeRange _getLastQuarterRange() {
    final now = DateTime.now();
    final currentQuarter = ((now.month - 1) ~/ 3) + 1;
    final lastQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
    final year = currentQuarter == 1 ? now.year - 1 : now.year;
    final firstMonthOfLastQuarter = (lastQuarter - 1) * 3 + 1;
    final firstDayOfLastQuarter = DateTime(year, firstMonthOfLastQuarter, 1);
    final lastDayOfLastQuarter = DateTime(year, firstMonthOfLastQuarter + 3, 0);
    return DateTimeRange(
      start: firstDayOfLastQuarter,
      end: lastDayOfLastQuarter,
    );
  }

  DateTimeRange _getThisYearRange() {
    final now = DateTime.now();
    final firstDayOfYear = DateTime(now.year, 1, 1);
    final lastDayOfYear = DateTime(now.year, 12, 31);
    return DateTimeRange(start: firstDayOfYear, end: lastDayOfYear);
  }

  DateTimeRange _getLastYearRange() {
    final now = DateTime.now();
    final firstDayOfLastYear = DateTime(now.year - 1, 1, 1);
    final lastDayOfLastYear = DateTime(now.year - 1, 12, 31);
    return DateTimeRange(start: firstDayOfLastYear, end: lastDayOfLastYear);
  }

  String _formatDateRange(DateTimeRange range) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final startFormatted =
        '${range.start.day.toString().padLeft(2, '0')} ${months[range.start.month - 1]} ${range.start.year}';
    final endFormatted =
        '${range.end.day.toString().padLeft(2, '0')} ${months[range.end.month - 1]} ${range.end.year}';

    if (range.start.day == range.end.day &&
        range.start.month == range.end.month &&
        range.start.year == range.end.year) {
      return startFormatted;
    }

    return '$startFormatted - $endFormatted';
  }

  Future<void> _showCustomDatePicker() async {
    final bloc = context.read<TransactionDetailsBloc>();
    final state = bloc.state;

    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: DateTimeRange(
        start: state.startDate,
        end: state.endDate,
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFF5A67D8)),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && mounted) {
      bloc.add(ChangeDateRange(startDate: picked.start, endDate: picked.end));
    }
  }
}
