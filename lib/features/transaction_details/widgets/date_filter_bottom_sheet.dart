import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/transaction_details_bloc.dart';
import '../bloc/transaction_details_event.dart';
import '../bloc/transaction_details_state.dart';

class DateFilterBottomSheet extends StatefulWidget {
  const DateFilterBottomSheet({super.key});

  @override
  State<DateFilterBottomSheet> createState() => _DateFilterBottomSheetState();
}

class _DateFilterBottomSheetState extends State<DateFilterBottomSheet>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    );
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _slideAnimation.value) * 300),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(children: [_buildHeader(), _buildDateOptions()]),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Drag handle
        Container(
          margin: const EdgeInsets.only(top: 8, bottom: 4),
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        // Header
        Container(
          padding: const EdgeInsets.fromLTRB(20, 8, 20, 12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(color: Colors.grey[100]!, width: 1),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.calendar_today_outlined,
                      color: Color(0xFF5A67D8),
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const AppText(
                    'Select Date Range',
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                  ),
                ],
              ),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(
                    Icons.close,
                    color: Colors.black54,
                    size: 18,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDateOptions() {
    return BlocBuilder<TransactionDetailsBloc, TransactionDetailsState>(
      builder: (context, state) {
        final dateOptions = [
          {'title': 'Today', 'range': _formatDateRange(_getTodayRange())},
          {
            'title': 'Yesterday',
            'range': _formatDateRange(_getYesterdayRange()),
          },
          {
            'title': 'Last 7 days',
            'range': _formatDateRange(_getLast7DaysRange()),
          },
          {
            'title': 'Last 30 days',
            'range': _formatDateRange(_getLast30DaysRange()),
          },
          {
            'title': 'This month',
            'range': _formatDateRange(_getThisMonthRange()),
          },
          {
            'title': 'Last month',
            'range': _formatDateRange(_getLastMonthRange()),
          },
          {
            'title': 'This quarter',
            'range': _formatDateRange(_getThisQuarterRange()),
          },
          {
            'title': 'Last quarter',
            'range': _formatDateRange(_getLastQuarterRange()),
          },
          {
            'title': 'This year',
            'range': _formatDateRange(_getThisYearRange()),
          },
          {
            'title': 'Last year',
            'range': _formatDateRange(_getLastYearRange()),
          },
          {'title': 'Custom', 'range': ''},
        ];

        return Expanded(
          child: AnimationLimiter(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: dateOptions.length,
              itemBuilder: (context, index) {
                final option = dateOptions[index];
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 300),
                  child: SlideAnimation(
                    verticalOffset: 30.0,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildDateOption(
                        option['title'] as String,
                        option['range'] as String,
                        state,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateOption(
    String title,
    String dateRange,
    TransactionDetailsState state,
  ) {
    final isSelected = _isDateRangeSelected(title, state);
    final isCustom = title == 'Custom';

    return GestureDetector(
      onTap: () async {
        HapticFeedback.lightImpact();
        if (isCustom) {
          Navigator.of(context).pop();
          await _showCustomDatePicker();
        } else {
          final dateRange = _getDateRangeForTitle(title);
          if (dateRange != null) {
            context.read<TransactionDetailsBloc>().add(
              ChangeDateRange(
                startDate: dateRange.start,
                endDate: dateRange.end,
                filterName: title,
              ),
            );
          }
          Navigator.of(context).pop();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey, width: 0.1)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    title,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  if (dateRange.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    AppText(dateRange, fontSize: 14, color: Colors.grey[600]),
                  ],
                ],
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                  width: 2,
                ),
                color: isSelected
                    ? const Color(0xFF5A67D8)
                    : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 14)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  bool _isDateRangeSelected(String title, TransactionDetailsState state) {
    return state.selectedDateFilterName == title;
  }

  DateTimeRange? _getDateRangeForTitle(String title) {
    switch (title) {
      case 'Today':
        return _getTodayRange();
      case 'Yesterday':
        return _getYesterdayRange();
      case 'Last 7 days':
        return _getLast7DaysRange();
      case 'Last 30 days':
        return _getLast30DaysRange();
      case 'This month':
        return _getThisMonthRange();
      case 'Last month':
        return _getLastMonthRange();
      case 'This quarter':
        return _getThisQuarterRange();
      case 'Last quarter':
        return _getLastQuarterRange();
      case 'This year':
        return _getThisYearRange();
      case 'Last year':
        return _getLastYearRange();
      default:
        return null;
    }
  }

  DateTimeRange _getTodayRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(start: today, end: today);
  }

  DateTimeRange _getYesterdayRange() {
    final now = DateTime.now();
    final yesterday = DateTime(
      now.year,
      now.month,
      now.day,
    ).subtract(const Duration(days: 1));
    return DateTimeRange(start: yesterday, end: yesterday);
  }

  DateTimeRange _getLast7DaysRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 6));
    return DateTimeRange(start: weekAgo, end: today);
  }

  DateTimeRange _getLast30DaysRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final monthAgo = today.subtract(const Duration(days: 29));
    return DateTimeRange(start: monthAgo, end: today);
  }

  DateTimeRange _getThisMonthRange() {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(start: firstDayOfMonth, end: today);
  }

  DateTimeRange _getLastMonthRange() {
    final now = DateTime.now();
    final firstDayOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final lastDayOfLastMonth = DateTime(now.year, now.month, 0);
    return DateTimeRange(start: firstDayOfLastMonth, end: lastDayOfLastMonth);
  }

  DateTimeRange _getThisQuarterRange() {
    final now = DateTime.now();
    final quarter = ((now.month - 1) ~/ 3) + 1;
    final firstMonthOfQuarter = (quarter - 1) * 3 + 1;
    final firstDayOfQuarter = DateTime(now.year, firstMonthOfQuarter, 1);
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(start: firstDayOfQuarter, end: today);
  }

  DateTimeRange _getLastQuarterRange() {
    final now = DateTime.now();
    final currentQuarter = ((now.month - 1) ~/ 3) + 1;
    final lastQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
    final year = currentQuarter == 1 ? now.year - 1 : now.year;
    final firstMonthOfLastQuarter = (lastQuarter - 1) * 3 + 1;
    final firstDayOfLastQuarter = DateTime(year, firstMonthOfLastQuarter, 1);
    final lastDayOfLastQuarter = DateTime(year, firstMonthOfLastQuarter + 3, 0);
    return DateTimeRange(
      start: firstDayOfLastQuarter,
      end: lastDayOfLastQuarter,
    );
  }

  DateTimeRange _getThisYearRange() {
    final now = DateTime.now();
    final firstDayOfYear = DateTime(now.year, 1, 1);
    final today = DateTime(now.year, now.month, now.day);
    return DateTimeRange(start: firstDayOfYear, end: today);
  }

  DateTimeRange _getLastYearRange() {
    final now = DateTime.now();
    final firstDayOfLastYear = DateTime(now.year - 1, 1, 1);
    final lastDayOfLastYear = DateTime(now.year - 1, 12, 31);
    return DateTimeRange(start: firstDayOfLastYear, end: lastDayOfLastYear);
  }

  String _formatDateRange(DateTimeRange range) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final startFormatted =
        '${range.start.day.toString().padLeft(2, '0')} ${months[range.start.month - 1]} ${range.start.year}';
    final endFormatted =
        '${range.end.day.toString().padLeft(2, '0')} ${months[range.end.month - 1]} ${range.end.year}';

    if (range.start.day == range.end.day &&
        range.start.month == range.end.month &&
        range.start.year == range.end.year) {
      return startFormatted;
    }

    return '$startFormatted - $endFormatted';
  }

  Future<void> _showCustomDatePicker() async {
    final bloc = context.read<TransactionDetailsBloc>();
    final state = bloc.state;

    final DateTimeRange? picked = await showModalBottomSheet<DateTimeRange>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CustomDateRangePickerBottomSheet(
        initialDateRange: DateTimeRange(
          start: state.startDate,
          end: state.endDate,
        ),
        firstDate: DateTime(2020),
        lastDate: DateTime.now().add(const Duration(days: 365)),
      ),
    );

    if (picked != null && mounted) {
      bloc.add(
        ChangeDateRange(
          startDate: picked.start,
          endDate: picked.end,
          filterName: 'Custom Range',
        ),
      );
    }
  }
}

class _CustomDateRangePickerBottomSheet extends StatefulWidget {
  final DateTimeRange initialDateRange;
  final DateTime firstDate;
  final DateTime lastDate;

  const _CustomDateRangePickerBottomSheet({
    required this.initialDateRange,
    required this.firstDate,
    required this.lastDate,
  });

  @override
  State<_CustomDateRangePickerBottomSheet> createState() =>
      _CustomDateRangePickerBottomSheetState();
}

class _CustomDateRangePickerBottomSheetState
    extends State<_CustomDateRangePickerBottomSheet> {
  late DateTimeRange selectedDateRange;
  bool isSelectingStartDate = true;
  DateTime? tempStartDate;

  @override
  void initState() {
    super.initState();
    selectedDateRange = widget.initialDateRange;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Drag handle
            Container(
              margin: const EdgeInsets.only(top: 8, bottom: 4),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(20, 8, 20, 12),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey[100]!, width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.calendar_today_outlined,
                          color: Color(0xFF5A67D8),
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const AppText(
                            'Select Date Range',
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.black87,
                          ),
                          const SizedBox(height: 2),
                          AppText(
                            isSelectingStartDate
                                ? 'Tap to select start date'
                                : 'Tap to select end date',
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ],
                      ),
                    ],
                  ),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(
                        Icons.close,
                        color: Colors.black54,
                        size: 18,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),

            // Date Range Picker
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: Theme.of(context).colorScheme.copyWith(
                    primary: const Color(0xFF5A67D8),
                    onPrimary: Colors.teal,
                    surface: Colors.white,
                    onSurface: Colors.black87,
                    secondary: const Color(0xFF5A67D8),
                    onSecondary: Colors.white,
                  ),
                  textTheme: Theme.of(context).textTheme.copyWith(
                    headlineSmall: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    titleMedium: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    bodyLarge: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.black87,
                    ),
                  ),
                ),
                child: CalendarDatePicker(
                  initialDate: selectedDateRange.start,
                  firstDate: widget.firstDate,
                  lastDate: widget.lastDate,
                  calendarDelegate: GregorianCalendarDelegate(),
                  onDateChanged: (DateTime date) {
                    HapticFeedback.lightImpact();
                    setState(() {
                      if (isSelectingStartDate) {
                        // First selection - start date
                        tempStartDate = date;
                        isSelectingStartDate = false;
                        selectedDateRange = DateTimeRange(
                          start: date,
                          end: selectedDateRange.end.isBefore(date)
                              ? date
                              : selectedDateRange.end,
                        );
                      } else {
                        // Second selection - end date
                        final startDate =
                            tempStartDate ?? selectedDateRange.start;
                        if (date.isBefore(startDate)) {
                          // If end date is before start date, swap them
                          selectedDateRange = DateTimeRange(
                            start: date,
                            end: startDate,
                          );
                        } else {
                          selectedDateRange = DateTimeRange(
                            start: startDate,
                            end: date,
                          );
                        }
                      }
                    });
                  },
                ),
              ),
            ),

            // Selected Date Range Display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF5A67D8).withValues(alpha: 0.05),
                border: Border(
                  top: BorderSide(color: Colors.grey[100]!, width: 1),
                  bottom: BorderSide(color: Colors.grey[100]!, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.date_range,
                    color: const Color(0xFF5A67D8),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const AppText(
                          'Selected Range',
                          fontSize: 12,
                          color: Colors.black54,
                          fontWeight: FontWeight.w500,
                        ),
                        const SizedBox(height: 2),
                        AppText(
                          _formatDateRange(selectedDateRange),
                          fontSize: 14,
                          color: Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        isSelectingStartDate = true;
                        tempStartDate = null;
                        selectedDateRange = widget.initialDateRange;
                      });
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: const Color(0xFF5A67D8),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                    ),
                    child: const AppText(
                      'Reset',
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF5A67D8),
                    ),
                  ),
                ],
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[100]!, width: 1),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                top: false,
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          side: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1.5,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          backgroundColor: Colors.white,
                        ),
                        child: const AppText(
                          'Cancel',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black54,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () {
                          HapticFeedback.mediumImpact();
                          Navigator.of(context).pop(selectedDateRange);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF5A67D8),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                          shadowColor: const Color(
                            0xFF5A67D8,
                          ).withValues(alpha: 0.3),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.check, size: 18, color: Colors.white),
                            SizedBox(width: 8),
                            AppText(
                              'Select Range',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateRange(DateTimeRange range) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final startFormatted =
        '${range.start.day.toString().padLeft(2, '0')} ${months[range.start.month - 1]} ${range.start.year}';
    final endFormatted =
        '${range.end.day.toString().padLeft(2, '0')} ${months[range.end.month - 1]} ${range.end.year}';

    if (range.start.day == range.end.day &&
        range.start.month == range.end.month &&
        range.start.year == range.end.year) {
      return startFormatted;
    }

    return '$startFormatted - $endFormatted';
  }
}
