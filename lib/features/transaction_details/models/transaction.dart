import 'package:equatable/equatable.dart';

enum TransactionType {
  allTransactions,
  invoice,
  receivedPayment,
  salesReturn,
  purchase,
  paymentOut,
  purchaseReturn,
  addMoney,
  reduceMoney,
  expense,
  creditNote,
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.allTransactions:
        return 'All Transactions';
      case TransactionType.invoice:
        return 'Invoice';
      case TransactionType.receivedPayment:
        return 'Received Payment';
      case TransactionType.salesReturn:
        return 'Sales Return';
      case TransactionType.purchase:
        return 'Purchase';
      case TransactionType.paymentOut:
        return 'Payment Out';
      case TransactionType.purchaseReturn:
        return 'Purchase Return';
      case TransactionType.addMoney:
        return 'Add Money';
      case TransactionType.reduceMoney:
        return 'Reduce Money';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.creditNote:
        return 'Credit Note';
    }
  }

  bool get isPositive {
    switch (this) {
      case TransactionType.receivedPayment:
      case TransactionType.addMoney:
      case TransactionType.invoice:
      case TransactionType.purchaseReturn:
        return true;
      case TransactionType.paymentOut:
      case TransactionType.reduceMoney:
      case TransactionType.purchase:
      case TransactionType.salesReturn:
      case TransactionType.expense:
      case TransactionType.creditNote:
        return false;
      case TransactionType.allTransactions:
        return true; // Default
    }
  }
}

class Transaction extends Equatable {
  final String id;
  final TransactionType type;
  final double amount;
  final DateTime date;
  final String description;
  final String? partyName;
  final String? referenceNumber;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.date,
    required this.description,
    this.partyName,
    this.referenceNumber,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  Transaction copyWith({
    String? id,
    TransactionType? type,
    double? amount,
    DateTime? date,
    String? description,
    String? partyName,
    String? referenceNumber,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      description: description ?? this.description,
      partyName: partyName ?? this.partyName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'amount': amount,
      'date': date.toIso8601String(),
      'description': description,
      'partyName': partyName,
      'referenceNumber': referenceNumber,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.allTransactions,
      ),
      amount: (json['amount'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      description: json['description'] as String,
      partyName: json['partyName'] as String?,
      referenceNumber: json['referenceNumber'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        amount,
        date,
        description,
        partyName,
        referenceNumber,
        notes,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Transaction(id: $id, type: $type, amount: $amount, date: $date, description: $description, partyName: $partyName, referenceNumber: $referenceNumber, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
