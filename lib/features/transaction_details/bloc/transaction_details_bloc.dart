import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import 'transaction_details_event.dart';
import 'transaction_details_state.dart';
import '../models/transaction.dart';

class TransactionDetailsBloc
    extends Bloc<TransactionDetailsEvent, TransactionDetailsState> {
  TransactionDetailsBloc() : super(TransactionDetailsState()) {
    on<LoadTransactionDetails>(_onLoadTransactionDetails);
    on<RefreshTransactionDetails>(_onRefreshTransactionDetails);
    on<FilterTransactionsByType>(_onFilterTransactionsByType);
    on<FilterTransactionsByStaff>(_onFilterTransactionsByStaff);
    on<ChangeDateRange>(_onChangeDateRange);
    on<ShowTransactionTypeFilter>(_onShowTransactionTypeFilter);
    on<HideTransactionTypeFilter>(_onHideTransactionTypeFilter);
    on<ShowStaffFilter>(_onShowStaffFilter);
    on<HideStaffFilter>(_onHideStaffFilter);
    on<ShowDateRangePicker>(_onShowDateRangePicker);
    on<HideDateRangePicker>(_onHideDateRangePicker);
    on<ShowDateFilterBottomSheet>(_onShowDateFilterBottomSheet);
    on<HideDateFilterBottomSheet>(_onHideDateFilterBottomSheet);
  }

  // Mock data for transactions
  final List<Transaction> _mockTransactions = [
    Transaction(
      id: const Uuid().v4(),
      type: TransactionType.addMoney,
      amount: 1200.0,
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'Add Money',
      partyName: 'abc',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Transaction(
      id: const Uuid().v4(),
      type: TransactionType.invoice,
      amount: 2500.0,
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'Invoice #001',
      partyName: 'Customer A',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    Transaction(
      id: const Uuid().v4(),
      type: TransactionType.receivedPayment,
      amount: 1800.0,
      date: DateTime.now().subtract(const Duration(days: 3)),
      description: 'Payment Received',
      partyName: 'Customer B',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    Transaction(
      id: const Uuid().v4(),
      type: TransactionType.expense,
      amount: 500.0,
      date: DateTime.now().subtract(const Duration(days: 4)),
      description: 'Office Supplies',
      partyName: 'Supplier C',
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
    ),
    Transaction(
      id: const Uuid().v4(),
      type: TransactionType.paymentOut,
      amount: 3000.0,
      date: DateTime.now().subtract(const Duration(days: 5)),
      description: 'Payment to Vendor',
      partyName: 'Vendor D',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];

  Future<void> _onLoadTransactionDetails(
    LoadTransactionDetails event,
    Emitter<TransactionDetailsState> emit,
  ) async {
    emit(state.copyWith(status: TransactionDetailsStatus.loading));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 800));

      final filteredTransactions = _filterTransactions(
        _mockTransactions,
        state.selectedTransactionType,
        state.selectedStaffFilter,
        state.startDate,
        state.endDate,
      );

      final totalAmount = _calculateTotalAmount(filteredTransactions);

      emit(
        state.copyWith(
          status: TransactionDetailsStatus.success,
          transactions: _mockTransactions,
          filteredTransactions: filteredTransactions,
          totalAmount: totalAmount,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: TransactionDetailsStatus.failure,
          errorMessage: 'Failed to load transactions: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onRefreshTransactionDetails(
    RefreshTransactionDetails event,
    Emitter<TransactionDetailsState> emit,
  ) async {
    // For refresh, show loading briefly
    emit(state.copyWith(status: TransactionDetailsStatus.loading));
    await Future.delayed(const Duration(milliseconds: 300));

    try {
      final filteredTransactions = _filterTransactions(
        _mockTransactions,
        state.selectedTransactionType,
        state.selectedStaffFilter,
        state.startDate,
        state.endDate,
      );

      final totalAmount = _calculateTotalAmount(filteredTransactions);

      emit(
        state.copyWith(
          status: TransactionDetailsStatus.success,
          transactions: _mockTransactions,
          filteredTransactions: filteredTransactions,
          totalAmount: totalAmount,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: TransactionDetailsStatus.failure,
          errorMessage: 'Failed to refresh transactions: ${e.toString()}',
        ),
      );
    }
  }

  void _onFilterTransactionsByType(
    FilterTransactionsByType event,
    Emitter<TransactionDetailsState> emit,
  ) {
    final filteredTransactions = _filterTransactions(
      state.transactions,
      event.transactionType,
      state.selectedStaffFilter,
      state.startDate,
      state.endDate,
    );

    final totalAmount = _calculateTotalAmount(filteredTransactions);

    emit(
      state.copyWith(
        selectedTransactionType: event.transactionType,
        filteredTransactions: filteredTransactions,
        totalAmount: totalAmount,
        showTransactionTypeFilter: false,
      ),
    );
  }

  void _onFilterTransactionsByStaff(
    FilterTransactionsByStaff event,
    Emitter<TransactionDetailsState> emit,
  ) {
    final filteredTransactions = _filterTransactions(
      state.transactions,
      state.selectedTransactionType,
      event.staffFilter,
      state.startDate,
      state.endDate,
    );

    final totalAmount = _calculateTotalAmount(filteredTransactions);

    emit(
      state.copyWith(
        selectedStaffFilter: event.staffFilter,
        filteredTransactions: filteredTransactions,
        totalAmount: totalAmount,
        showStaffFilter: false,
      ),
    );
  }

  void _onChangeDateRange(
    ChangeDateRange event,
    Emitter<TransactionDetailsState> emit,
  ) {
    final filteredTransactions = _filterTransactions(
      state.transactions,
      state.selectedTransactionType,
      state.selectedStaffFilter,
      event.startDate,
      event.endDate,
    );

    final totalAmount = _calculateTotalAmount(filteredTransactions);

    emit(
      state.copyWith(
        startDate: event.startDate,
        endDate: event.endDate,
        selectedDateFilterName:
            event.filterName ?? state.selectedDateFilterName,
        filteredTransactions: filteredTransactions,
        totalAmount: totalAmount,
        showDateRangePicker: false,
        showDateFilterBottomSheet: false,
      ),
    );
  }

  void _onShowTransactionTypeFilter(
    ShowTransactionTypeFilter event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(
      state.copyWith(
        showTransactionTypeFilter: true,
        showStaffFilter: false,
        showDateRangePicker: false,
      ),
    );
  }

  void _onHideTransactionTypeFilter(
    HideTransactionTypeFilter event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(state.copyWith(showTransactionTypeFilter: false));
  }

  void _onShowStaffFilter(
    ShowStaffFilter event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(
      state.copyWith(
        showStaffFilter: true,
        showTransactionTypeFilter: false,
        showDateRangePicker: false,
      ),
    );
  }

  void _onHideStaffFilter(
    HideStaffFilter event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(state.copyWith(showStaffFilter: false));
  }

  void _onShowDateRangePicker(
    ShowDateRangePicker event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(
      state.copyWith(
        showDateRangePicker: true,
        showTransactionTypeFilter: false,
        showStaffFilter: false,
      ),
    );
  }

  void _onHideDateRangePicker(
    HideDateRangePicker event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(state.copyWith(showDateRangePicker: false));
  }

  List<Transaction> _filterTransactions(
    List<Transaction> transactions,
    TransactionType transactionType,
    String staffFilter,
    DateTime startDate,
    DateTime endDate,
  ) {
    return transactions.where((transaction) {
      // Filter by date range
      final transactionDate = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      final startDateOnly = DateTime(
        startDate.year,
        startDate.month,
        startDate.day,
      );
      final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

      final isInDateRange =
          transactionDate.isAfter(
            startDateOnly.subtract(const Duration(days: 1)),
          ) &&
          transactionDate.isBefore(endDateOnly.add(const Duration(days: 1)));

      // Filter by transaction type
      final matchesType =
          transactionType == TransactionType.allTransactions ||
          transaction.type == transactionType;

      // Filter by staff (mock implementation)
      final matchesStaff =
          staffFilter == 'All Staff' ||
          true; // Mock: all transactions match staff filter

      return isInDateRange && matchesType && matchesStaff;
    }).toList();
  }

  double _calculateTotalAmount(List<Transaction> transactions) {
    return transactions.fold(0.0, (total, transaction) {
      return total +
          (transaction.type.isPositive
              ? transaction.amount
              : -transaction.amount);
    });
  }

  void _onShowDateFilterBottomSheet(
    ShowDateFilterBottomSheet event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(
      state.copyWith(
        showDateFilterBottomSheet: true,
        showTransactionTypeFilter: false,
        showStaffFilter: false,
        showDateRangePicker: false,
      ),
    );
  }

  void _onHideDateFilterBottomSheet(
    HideDateFilterBottomSheet event,
    Emitter<TransactionDetailsState> emit,
  ) {
    emit(state.copyWith(showDateFilterBottomSheet: false));
  }
}
