import 'package:equatable/equatable.dart';
import '../models/transaction.dart';

enum TransactionDetailsStatus { initial, loading, success, failure }

class TransactionDetailsState extends Equatable {
  final TransactionDetailsStatus status;
  final List<Transaction> transactions;
  final List<Transaction> filteredTransactions;
  final TransactionType selectedTransactionType;
  final String selectedStaffFilter;
  final DateTime startDate;
  final DateTime endDate;
  final String? errorMessage;
  final bool showTransactionTypeFilter;
  final bool showStaffFilter;
  final bool showDateRangePicker;
  final double totalAmount;

  TransactionDetailsState({
    this.status = TransactionDetailsStatus.initial,
    this.transactions = const [],
    this.filteredTransactions = const [],
    this.selectedTransactionType = TransactionType.allTransactions,
    this.selectedStaffFilter = 'All Staff',
    DateTime? startDate,
    DateTime? endDate,
    this.errorMessage,
    this.showTransactionTypeFilter = false,
    this.showStaffFilter = false,
    this.showDateRangePicker = false,
    this.totalAmount = 0.0,
  }) : startDate = startDate ?? DateTime.now().subtract(const Duration(days: 7)),
       endDate = endDate ?? DateTime.now();

  TransactionDetailsState copyWith({
    TransactionDetailsStatus? status,
    List<Transaction>? transactions,
    List<Transaction>? filteredTransactions,
    TransactionType? selectedTransactionType,
    String? selectedStaffFilter,
    DateTime? startDate,
    DateTime? endDate,
    String? errorMessage,
    bool? showTransactionTypeFilter,
    bool? showStaffFilter,
    bool? showDateRangePicker,
    double? totalAmount,
  }) {
    return TransactionDetailsState(
      status: status ?? this.status,
      transactions: transactions ?? this.transactions,
      filteredTransactions: filteredTransactions ?? this.filteredTransactions,
      selectedTransactionType: selectedTransactionType ?? this.selectedTransactionType,
      selectedStaffFilter: selectedStaffFilter ?? this.selectedStaffFilter,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      errorMessage: errorMessage,
      showTransactionTypeFilter: showTransactionTypeFilter ?? this.showTransactionTypeFilter,
      showStaffFilter: showStaffFilter ?? this.showStaffFilter,
      showDateRangePicker: showDateRangePicker ?? this.showDateRangePicker,
      totalAmount: totalAmount ?? this.totalAmount,
    );
  }

  String get dateRangeText {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekAgo = today.subtract(const Duration(days: 7));
    final monthAgo = DateTime(now.year, now.month - 1, now.day);

    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

    if (startDateOnly == today && endDateOnly == today) {
      return 'Today';
    } else if (startDateOnly == yesterday && endDateOnly == yesterday) {
      return 'Yesterday';
    } else if (startDateOnly == weekAgo && endDateOnly == today) {
      return 'Last 7 days';
    } else if (startDateOnly.year == monthAgo.year && 
               startDateOnly.month == monthAgo.month && 
               endDateOnly == today) {
      return 'Last 30 days';
    } else {
      return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
    }
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  List<String> get availableStaffFilters {
    return ['All Staff', 'Staff 1', 'Staff 2', 'Staff 3']; // Mock data
  }

  @override
  List<Object?> get props => [
        status,
        transactions,
        filteredTransactions,
        selectedTransactionType,
        selectedStaffFilter,
        startDate,
        endDate,
        errorMessage,
        showTransactionTypeFilter,
        showStaffFilter,
        showDateRangePicker,
        totalAmount,
      ];
}
