import 'package:equatable/equatable.dart';
import '../models/transaction.dart';

abstract class TransactionDetailsEvent extends Equatable {
  const TransactionDetailsEvent();

  @override
  List<Object?> get props => [];
}

class LoadTransactionDetails extends TransactionDetailsEvent {
  const LoadTransactionDetails();
}

class RefreshTransactionDetails extends TransactionDetailsEvent {
  const RefreshTransactionDetails();
}

class FilterTransactionsByType extends TransactionDetailsEvent {
  final TransactionType transactionType;

  const FilterTransactionsByType(this.transactionType);

  @override
  List<Object?> get props => [transactionType];
}

class FilterTransactionsByStaff extends TransactionDetailsEvent {
  final String staffFilter;

  const FilterTransactionsByStaff(this.staffFilter);

  @override
  List<Object?> get props => [staffFilter];
}

class ChangeDateRange extends TransactionDetailsEvent {
  final DateTime startDate;
  final DateTime endDate;

  const ChangeDateRange({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}

class ShowTransactionTypeFilter extends TransactionDetailsEvent {
  const ShowTransactionTypeFilter();
}

class HideTransactionTypeFilter extends TransactionDetailsEvent {
  const HideTransactionTypeFilter();
}

class ShowStaffFilter extends TransactionDetailsEvent {
  const ShowStaffFilter();
}

class HideStaffFilter extends TransactionDetailsEvent {
  const HideStaffFilter();
}

class ShowDateRangePicker extends TransactionDetailsEvent {
  const ShowDateRangePicker();
}

class HideDateRangePicker extends TransactionDetailsEvent {
  const HideDateRangePicker();
}
