import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/transaction_details_bloc.dart';
import '../bloc/transaction_details_event.dart';
import '../bloc/transaction_details_state.dart';
import '../models/transaction.dart';
import '../widgets/transaction_filter_bottom_sheet.dart';
import '../widgets/date_filter_bottom_sheet.dart';

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({super.key});

  @override
  State<TransactionDetailsScreen> createState() =>
      _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          TransactionDetailsBloc()..add(const LoadTransactionDetails()),
      child: BlocListener<TransactionDetailsBloc, TransactionDetailsState>(
        listener: (context, state) {
          if (state.showTransactionTypeFilter) {
            _showTransactionTypeFilter(context);
          }
          if (state.showDateFilterBottomSheet) {
            _showDateFilterBottomSheet(context);
          }
        },
        child: Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: _buildAppBar(),
          body: SafeArea(child: _buildBody()),
          floatingActionButton: _buildFloatingActionButton(),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Platform.isIOS ? Icons.arrow_back_ios_new_rounded : Icons.arrow_back,
          color: Colors.black87,
        ),
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
      ),
      title: const AppText(
        'Total Cash & Bank Balance',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.picture_as_pdf, color: Colors.orange),
          onPressed: () {
            HapticFeedback.lightImpact();
            // Handle PDF export
          },
        ),
        IconButton(
          icon: const Icon(Icons.table_chart, color: Colors.green),
          onPressed: () {
            HapticFeedback.lightImpact();
            // Handle Excel export
          },
        ),
      ],
    );
  }

  Widget _buildBody() {
    return BlocBuilder<TransactionDetailsBloc, TransactionDetailsState>(
      builder: (context, state) {
        return Column(
          children: [
            _buildDateRangeSection(context, state),
            _buildFilterSection(context, state),
            Expanded(child: _buildTransactionsList(context, state)),
          ],
        );
      },
    );
  }

  Widget _buildDateRangeSection(
    BuildContext context,
    TransactionDetailsState state,
  ) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: 30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.grey, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Row(
                    children: [
                      AppText(
                        state.dateRangeText,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: AppText(
                          state.formattedDateRange,
                          fontSize: 12,
                          color: Colors.grey[600],
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    context.read<TransactionDetailsBloc>().add(
                      const ShowDateFilterBottomSheet(),
                    );
                  },
                  child: const AppText(
                    'CHANGE',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF5A67D8),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection(
    BuildContext context,
    TransactionDetailsState state,
  ) {
    return AnimationConfiguration.staggeredList(
      position: 1,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: 30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: _buildFilterDropdown(
                    context,
                    state.selectedTransactionType.displayName,
                    Icons.keyboard_arrow_down,
                    () {
                      HapticFeedback.lightImpact();
                      context.read<TransactionDetailsBloc>().add(
                        const ShowTransactionTypeFilter(),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildFilterDropdown(
                    context,
                    state.selectedStaffFilter,
                    Icons.keyboard_arrow_down,
                    () {
                      HapticFeedback.lightImpact();
                      context.read<TransactionDetailsBloc>().add(
                        const ShowStaffFilter(),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterDropdown(
    BuildContext context,
    String text,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Expanded(
              child: AppText(
                text,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Icon(icon, color: Colors.grey[600], size: 18),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(
    BuildContext context,
    TransactionDetailsState state,
  ) {
    if (state.status == TransactionDetailsStatus.loading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF5A67D8)),
      );
    }

    if (state.status == TransactionDetailsStatus.failure) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            AppText(
              state.errorMessage ?? 'Failed to load transactions',
              fontSize: 16,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                context.read<TransactionDetailsBloc>().add(
                  const RefreshTransactionDetails(),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5A67D8),
                foregroundColor: Colors.white,
              ),
              child: const AppText('Retry', fontSize: 14, color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (state.filteredTransactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            AppText('No transactions found', fontSize: 16, color: Colors.grey),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<TransactionDetailsBloc>().add(
          const RefreshTransactionDetails(),
        );
      },
      child: AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.filteredTransactions.length,
          itemBuilder: (context, index) {
            final transaction = state.filteredTransactions[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 400),
              child: SlideAnimation(
                verticalOffset: 30.0,
                curve: Curves.easeOutCubic,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: _buildTransactionItem(transaction),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTransactionItem(Transaction transaction) {
    final isPositive = transaction.type.isPositive;
    final formattedAmount = NumberFormat.currency(
      symbol: '₹ ',
      decimalDigits: 0,
    ).format(transaction.amount);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AppText(
                      DateFormat('dd.MM.yyyy').format(transaction.date),
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    AppText(
                      '1', // Transaction number placeholder
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                AppText(
                  transaction.description,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                if (transaction.partyName != null) ...[
                  const SizedBox(height: 4),
                  AppText(
                    transaction.partyName!,
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ],
              ],
            ),
          ),
          AppText(
            '${isPositive ? '+' : '-'} $formattedAmount',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isPositive ? Colors.green : Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(0, 2), end: Offset.zero)
          .animate(
            CurvedAnimation(
              parent: _slideController,
              curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
            ),
          ),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: ElevatedButton(
          onPressed: () {
            HapticFeedback.mediumImpact();
            _showAdjustBalanceBottomSheet(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF5A67D8),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            elevation: 4,
          ),
          child: const AppText(
            'Adjust Balance',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _showAdjustBalanceBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: Navigator.of(context),
      ),
      builder: (context) => const AdjustBalanceBottomSheet(),
    );
  }

  void _showTransactionTypeFilter(BuildContext context) {
    final bloc = context.read<TransactionDetailsBloc>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: bloc,
        child: const TransactionFilterBottomSheet(),
      ),
    ).then((_) {
      // Hide the filter state when bottom sheet is dismissed
      if (mounted) {
        bloc.add(const HideTransactionTypeFilter());
      }
    });
  }

  void _showDateFilterBottomSheet(BuildContext context) {
    final bloc = context.read<TransactionDetailsBloc>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) =>
          BlocProvider.value(value: bloc, child: const DateFilterBottomSheet()),
    ).then((_) {
      // Hide the filter state when bottom sheet is dismissed
      if (mounted) {
        bloc.add(const HideDateFilterBottomSheet());
      }
    });
  }
}
