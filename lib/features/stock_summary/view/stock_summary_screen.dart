import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/widgets/custom_info_tooltip.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/constants/app_colors.dart';
import '../bloc/stock_summary_bloc.dart';
import '../bloc/stock_summary_event.dart';
import '../bloc/stock_summary_state.dart';
import '../models/stock_item.dart';

class StockSummaryScreen extends StatelessWidget {
  const StockSummaryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => StockSummaryBloc()..add(const LoadStockSummary()),
      child: const StockSummaryView(),
    );
  }
}

class StockSummaryView extends StatefulWidget {
  const StockSummaryView({super.key});

  @override
  State<StockSummaryView> createState() => _StockSummaryViewState();
}

class _StockSummaryViewState extends State<StockSummaryView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppText(
          'Stock Summary',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf, color: Colors.orange),
            onPressed: () {
              context.read<StockSummaryBloc>().add(const ExportStockReport());
            },
          ),
          IconButton(
            icon: const Icon(Icons.table_chart, color: Colors.green),
            onPressed: () {
              // Handle Excel export
            },
          ),
        ],
      ),
      body: BlocConsumer<StockSummaryBloc, StockSummaryState>(
        listener: (context, state) {
          if (state is StockSummaryError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          } else if (state is StockReportExported) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Report exported successfully!')),
            );
          }
        },
        builder: (context, state) {
          if (state is StockSummaryLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is StockSummaryLoaded) {
            return Stack(
              children: [
                _buildLoadedContent(context, state),
                // Overlay for progress dialog
                BlocBuilder<StockSummaryBloc, StockSummaryState>(
                  builder: (context, overlayState) {
                    if (overlayState is StockReportExporting) {
                      return _buildProgressOverlay(overlayState);
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            );
          } else if (state is StockReportExporting) {
            return Stack(
              children: [
                // Show a basic loading state as background
                const Center(child: CircularProgressIndicator()),
                _buildProgressOverlay(state),
              ],
            );
          } else if (state is StockSummaryError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  AppText(state.message, textAlign: TextAlign.center),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<StockSummaryBloc>().add(
                        const LoadStockSummary(),
                      );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildLoadedContent(BuildContext context, StockSummaryLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<StockSummaryBloc>().add(const RefreshStockSummary());
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: AnimationLimiter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 400),
              delay: const Duration(milliseconds: 100),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 30.0,
                curve: Curves.easeOutCubic,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: widget,
                ),
              ),
              children: [
                // Category Filter - fade in from top with slight slide down
                AnimationConfiguration.staggeredList(
                  position: 0,
                  duration: const Duration(milliseconds: 350),
                  child: SlideAnimation(
                    verticalOffset: -20.0,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildCategoryFilter(context, state),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Total Stock Value Card - scale up from center with fade in
                AnimationConfiguration.staggeredList(
                  position: 1,
                  duration: const Duration(milliseconds: 400),
                  child: ScaleAnimation(
                    scale: 0.8,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildTotalValueCard(state.stockSummary),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Items List Header - slide in from left with fade in
                AnimationConfiguration.staggeredList(
                  position: 2,
                  duration: const Duration(milliseconds: 350),
                  child: SlideAnimation(
                    horizontalOffset: -30.0,
                    curve: Curves.easeOutCubic,
                    child: FadeInAnimation(
                      curve: Curves.easeOutCubic,
                      child: _buildItemsHeader(),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Items List - staggered fade in from bottom
                _buildAnimatedItemsList(state.stockSummary.items),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryFilter(BuildContext context, StockSummaryLoaded state) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.filter_list_rounded,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: state.stockSummary.selectedCategory,
                isExpanded: true,
                isDense: true,
                icon: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
                dropdownColor: AppColors.surface,
                elevation: 8,
                borderRadius: BorderRadius.circular(12),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
                items: state.categories.map((String category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color:
                                category == state.stockSummary.selectedCategory
                                ? AppColors.primary
                                : Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            Icons.category_outlined,
                            color:
                                category == state.stockSummary.selectedCategory
                                ? AppColors.onPrimary
                                : Colors.grey[600],
                            size: 12,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: AppText(
                            category,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color:
                                category == state.stockSummary.selectedCategory
                                ? AppColors.primary
                                : AppColors.onSurface,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    HapticFeedback.lightImpact();
                    context.read<StockSummaryBloc>().add(
                      FilterByCategory(newValue),
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalValueCard(StockSummary summary) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                const AppText(
                  'Total Stock Value',
                  fontSize: 14,
                  color: Colors.black54,
                ),
                const SizedBox(width: 8),
                const CustomInfoTooltip(
                  content:
                      'Total stock value is calculated only for the first 500 items. To get accurate value please use the send report to email option.',
                  iconSize: 16,
                  position: ElTooltipPosition.topCenter,
                  distance: 5,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          AppText(
            '₹ ${summary.totalValue.toStringAsFixed(2)}',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ],
      ),
    );
  }

  Widget _buildItemsHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: const Row(
        children: [
          Expanded(
            flex: 3,
            child: AppText(
              'Item Name',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.black54,
            ),
          ),
          Expanded(
            flex: 2,
            child: AppText(
              'Quantity',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.black54,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                AppText(
                  'Value',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.black54,
                ),
                SizedBox(width: 4),
                CustomInfoTooltip(
                  content:
                      'For items with batches, stock value is calculated using the price of the batch with earliest expiry date/created date',
                  iconSize: 14,
                  position: ElTooltipPosition.topCenter,
                  distance: 5,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  iconColor: Colors.grey,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedItemsList(List<StockItem> items) {
    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 300),
          delay: const Duration(milliseconds: 150),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 40.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
          ),
          children: items.map((item) => _buildItemCard(item)).toList(),
        ),
      ),
    );
  }

  Widget _buildItemCard(StockItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(item.id, fontSize: 12, color: Colors.black54),
                const SizedBox(height: 4),
                AppText(
                  item.name,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: AppText(
              '${item.quantity.toStringAsFixed(1)} ${item.unit}',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: AppText(
              '₹ ${item.value.toStringAsFixed(2)}',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressOverlay(StockReportExporting state) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5), // Semi-transparent background
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.picture_as_pdf, size: 48, color: Colors.orange),
              const SizedBox(height: 16),
              const AppText(
                'Generating PDF Report',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Circular progress with percentage
              Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CircularProgressIndicator(
                      value: state.progress,
                      strokeWidth: 6,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Colors.orange,
                      ),
                    ),
                  ),
                  AppText(
                    '${(state.progress * 100).toInt()}%',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Current step
              AppText(
                state.currentStep,
                fontSize: 14,
                color: Colors.black54,
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Linear progress bar
              Column(
                children: [
                  LinearProgressIndicator(
                    value: state.progress,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const AppText('0%', fontSize: 12, color: Colors.black54),
                      AppText(
                        '${(state.progress * 100).toInt()}%',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.orange,
                      ),
                      const AppText(
                        '100%',
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
