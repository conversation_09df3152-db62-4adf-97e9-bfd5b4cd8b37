import 'package:equatable/equatable.dart';

class StockItem extends Equatable {
  final String id;
  final String name;
  final double quantity;
  final String unit;
  final double value;

  const StockItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.unit,
    required this.value,
  });

  @override
  List<Object?> get props => [id, name, quantity, unit, value];

  factory StockItem.fromJson(Map<String, dynamic> json) {
    return StockItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      quantity: (json['quantity'] ?? 0).toDouble(),
      unit: json['unit'] ?? '',
      value: (json['value'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'quantity': quantity,
      'unit': unit,
      'value': value,
    };
  }
}

class StockSummary extends Equatable {
  final double totalValue;
  final List<StockItem> items;
  final String selectedCategory;

  const StockSummary({
    required this.totalValue,
    required this.items,
    this.selectedCategory = 'All Categories',
  });

  @override
  List<Object?> get props => [totalValue, items, selectedCategory];

  StockSummary copyWith({
    double? totalValue,
    List<StockItem>? items,
    String? selectedCategory,
  }) {
    return StockSummary(
      totalValue: totalValue ?? this.totalValue,
      items: items ?? this.items,
      selectedCategory: selectedCategory ?? this.selectedCategory,
    );
  }
}
