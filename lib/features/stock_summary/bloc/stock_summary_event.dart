import 'package:equatable/equatable.dart';

abstract class StockSummaryEvent extends Equatable {
  const StockSummaryEvent();

  @override
  List<Object?> get props => [];
}

class LoadStockSummary extends StockSummaryEvent {
  const LoadStockSummary();
}

class FilterByCategory extends StockSummaryEvent {
  final String category;

  const FilterByCategory(this.category);

  @override
  List<Object?> get props => [category];
}

class RefreshStockSummary extends StockSummaryEvent {
  const RefreshStockSummary();
}

class ExportStockReport extends StockSummaryEvent {
  const ExportStockReport();
}
