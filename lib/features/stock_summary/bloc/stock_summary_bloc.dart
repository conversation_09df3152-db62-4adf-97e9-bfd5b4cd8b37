import 'package:flutter_bloc/flutter_bloc.dart';
import 'stock_summary_event.dart';
import 'stock_summary_state.dart';
import '../models/stock_item.dart';

class StockSummaryBloc extends Bloc<StockSummaryEvent, StockSummaryState> {
  StockSummaryBloc() : super(const StockSummaryInitial()) {
    on<LoadStockSummary>(_onLoadStockSummary);
    on<FilterByCategory>(_onFilterByCategory);
    on<RefreshStockSummary>(_onRefreshStockSummary);
    on<ExportStockReport>(_onExportStockReport);
  }

  // Mock data - replace with actual API calls
  final List<StockItem> _allItems = [
    const StockItem(
      id: '890126210023',
      name: 'Sample Item',
      quantity: 123.0,
      unit: 'BOX',
      value: 20866.07,
    ),
    const StockItem(
      id: 'hi001',
      name: 'hi',
      quantity: 158.0,
      unit: 'PCS',
      value: 1580.0,
    ),
  ];

  final List<String> _categories = [
    'All Categories',
    'Electronics',
    'Clothing',
    'Food & Beverages',
    'Home & Garden',
    'Sports',
  ];

  Future<void> _onLoadStockSummary(
    LoadStockSummary event,
    Emitter<StockSummaryState> emit,
  ) async {
    emit(const StockSummaryLoading());

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      final totalValue = _allItems.fold<double>(
        0.0,
        (sum, item) => sum + item.value,
      );

      final stockSummary = StockSummary(
        totalValue: totalValue,
        items: _allItems,
        selectedCategory: 'All Categories',
      );

      emit(
        StockSummaryLoaded(stockSummary: stockSummary, categories: _categories),
      );
    } catch (e) {
      emit(StockSummaryError('Failed to load stock summary: ${e.toString()}'));
    }
  }

  Future<void> _onFilterByCategory(
    FilterByCategory event,
    Emitter<StockSummaryState> emit,
  ) async {
    if (state is StockSummaryLoaded) {
      final currentState = state as StockSummaryLoaded;

      List<StockItem> filteredItems;
      if (event.category == 'All Categories') {
        filteredItems = _allItems;
      } else {
        // Mock filtering - in real app, filter by actual category
        filteredItems = _allItems;
      }

      final totalValue = filteredItems.fold<double>(
        0.0,
        (sum, item) => sum + item.value,
      );

      final updatedSummary = currentState.stockSummary.copyWith(
        items: filteredItems,
        totalValue: totalValue,
        selectedCategory: event.category,
      );

      emit(
        StockSummaryLoaded(
          stockSummary: updatedSummary,
          categories: currentState.categories,
        ),
      );
    }
  }

  Future<void> _onRefreshStockSummary(
    RefreshStockSummary event,
    Emitter<StockSummaryState> emit,
  ) async {
    add(const LoadStockSummary());
  }

  Future<void> _onExportStockReport(
    ExportStockReport event,
    Emitter<StockSummaryState> emit,
  ) async {
    if (state is StockSummaryLoaded) {
      try {
        // Step 1: Preparing data
        emit(
          const StockReportExporting(
            progress: 0.1,
            currentStep: 'Preparing stock data...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 500));

        // Step 2: Processing items
        emit(
          const StockReportExporting(
            progress: 0.3,
            currentStep: 'Processing stock items...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 600));

        // Step 3: Calculating totals
        emit(
          const StockReportExporting(
            progress: 0.5,
            currentStep: 'Calculating totals...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 400));

        // Step 4: Generating PDF structure
        emit(
          const StockReportExporting(
            progress: 0.7,
            currentStep: 'Creating PDF structure...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 500));

        // Step 5: Adding content to PDF
        emit(
          const StockReportExporting(
            progress: 0.85,
            currentStep: 'Adding content to PDF...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 400));

        // Step 6: Finalizing PDF
        emit(
          const StockReportExporting(
            progress: 0.95,
            currentStep: 'Finalizing PDF...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 300));

        // Step 7: Saving file
        emit(
          const StockReportExporting(
            progress: 1.0,
            currentStep: 'Saving file...',
          ),
        );
        await Future.delayed(const Duration(milliseconds: 200));

        // Mock file path - in real app, generate actual PDF/Excel file
        const filePath = '/storage/emulated/0/Download/stock_report.pdf';

        emit(const StockReportExported(filePath));

        // Return to loaded state
        await Future.delayed(const Duration(milliseconds: 500));
        add(const LoadStockSummary());
      } catch (e) {
        emit(StockSummaryError('Failed to export report: ${e.toString()}'));
      }
    }
  }
}
