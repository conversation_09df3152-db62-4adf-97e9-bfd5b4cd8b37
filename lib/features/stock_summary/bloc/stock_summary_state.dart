import 'package:equatable/equatable.dart';
import '../models/stock_item.dart';

abstract class StockSummaryState extends Equatable {
  const StockSummaryState();

  @override
  List<Object?> get props => [];
}

class StockSummaryInitial extends StockSummaryState {
  const StockSummaryInitial();
}

class StockSummaryLoading extends StockSummaryState {
  const StockSummaryLoading();
}

class StockSummaryLoaded extends StockSummaryState {
  final StockSummary stockSummary;
  final List<String> categories;

  const StockSummaryLoaded({
    required this.stockSummary,
    required this.categories,
  });

  @override
  List<Object?> get props => [stockSummary, categories];
}

class StockSummaryError extends StockSummaryState {
  final String message;

  const StockSummaryError(this.message);

  @override
  List<Object?> get props => [message];
}

class StockReportExporting extends StockSummaryState {
  final double progress;
  final String currentStep;

  const StockReportExporting({
    this.progress = 0.0,
    this.currentStep = 'Preparing report...',
  });

  @override
  List<Object?> get props => [progress, currentStep];
}

class StockReportExported extends StockSummaryState {
  final String filePath;

  const StockReportExported(this.filePath);

  @override
  List<Object?> get props => [filePath];
}
