import 'package:equatable/equatable.dart';

abstract class ItemDetailsEvent extends Equatable {
  const ItemDetailsEvent();

  @override
  List<Object?> get props => [];
}

class LoadItemDetails extends ItemDetailsEvent {
  final Map<String, dynamic> item;

  const LoadItemDetails(this.item);

  @override
  List<Object?> get props => [item];
}

class TabChanged extends ItemDetailsEvent {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class AdjustStockPressed extends ItemDetailsEvent {
  const AdjustStockPressed();
}

class SharePressed extends ItemDetailsEvent {
  const SharePressed();
}

class EditPressed extends ItemDetailsEvent {
  const EditPressed();
}

class DeletePressed extends ItemDetailsEvent {
  const DeletePressed();
}
