import 'package:equatable/equatable.dart';

enum ItemDetailsStatus { initial, loading, loaded, error }

class ItemDetailsState extends Equatable {
  final ItemDetailsStatus status;
  final Map<String, dynamic>? item;
  final int currentTabIndex;
  final String? errorMessage;

  const ItemDetailsState({
    this.status = ItemDetailsStatus.initial,
    this.item,
    this.currentTabIndex = 0,
    this.errorMessage,
  });

  ItemDetailsState copyWith({
    ItemDetailsStatus? status,
    Map<String, dynamic>? item,
    int? currentTabIndex,
    String? errorMessage,
  }) {
    return ItemDetailsState(
      status: status ?? this.status,
      item: item ?? this.item,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, item, currentTabIndex, errorMessage];
}
