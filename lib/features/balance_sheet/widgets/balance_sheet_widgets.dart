import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../models/balance_sheet_item.dart';

class BalanceSheetItemWidget extends StatelessWidget {
  final BalanceSheetItem item;
  final VoidCallback? onTap;

  const BalanceSheetItemWidget({super.key, required this.item, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: const BoxDecoration(color: Colors.white),
      child: Column(
        children: [
          _buildMainItem(),
          if (item.isExpandable && item.isExpanded && item.subItems != null)
            _buildSubItems(),
        ],
      ),
    );
  }

  Widget _buildMainItem() {
    final formatter = NumberFormat('#,##0.00');

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Icon
            if (item.icon != null)
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: (item.iconColor ?? Colors.grey).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  item.icon,
                  color: item.iconColor ?? Colors.grey,
                  size: 18,
                ),
              ),
            const SizedBox(width: 12),

            // Title and Info Icon
            Expanded(
              child: Row(
                children: [
                  AppText(
                    item.title,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  const SizedBox(width: 8),
                  Icon(Icons.info_outline, size: 16, color: Colors.grey[400]),
                ],
              ),
            ),

            // Amount
            AppText(
              '₹ ${formatter.format(item.amount)}',
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),

            // Expansion arrow
            if (item.isExpandable)
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: AnimatedRotation(
                  turns: item.isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubItems() {
    if (item.subItems == null || item.subItems!.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedSize(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 200),
            delay: const Duration(milliseconds: 50),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
            ),
            children: item.subItems!
                .map((subItem) => BalanceSheetSubItemWidget(subItem: subItem))
                .toList(),
          ),
        ),
      ),
    );
  }
}

class BalanceSheetSubItemWidget extends StatelessWidget {
  final BalanceSheetSubItem subItem;

  const BalanceSheetSubItemWidget({super.key, required this.subItem});

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat('#,##0.00');

    return Container(
      padding: const EdgeInsets.only(left: 64, right: 16, top: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(left: BorderSide(color: Colors.grey[300]!, width: 2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppText(
            subItem.title,
            fontSize: 13,
            fontWeight: FontWeight.normal,
            color: Colors.grey[700],
          ),
          AppText(
            '₹ ${formatter.format(subItem.amount)}',
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ],
      ),
    );
  }
}

class BalanceSheetSummaryCard extends StatelessWidget {
  final String title;
  final double amount;
  final Color backgroundColor;
  final Color textColor;
  final IconData? icon;

  const BalanceSheetSummaryCard({
    super.key,
    required this.title,
    required this.amount,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat('#,##0.00');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, color: textColor.withValues(alpha: 0.7), size: 24),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  title,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: textColor.withValues(alpha: 0.8),
                ),
                const SizedBox(height: 4),
                AppText(
                  '₹ ${formatter.format(amount)}',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class BalanceSheetErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const BalanceSheetErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            AppText(
              'Oops! Something went wrong',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            AppText(
              message,
              fontSize: 14,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
              maxLines: 3,
            ),
            const SizedBox(height: 24),
            if (onRetry != null)
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF5A67D8),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const AppText(
                  'Try Again',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class BalanceSheetLoadingWidget extends StatelessWidget {
  const BalanceSheetLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Color(0xFF5A67D8),
            strokeWidth: 3,
          ),
          const SizedBox(height: 16),
          AppText(
            'Loading balance sheet...',
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }
}
