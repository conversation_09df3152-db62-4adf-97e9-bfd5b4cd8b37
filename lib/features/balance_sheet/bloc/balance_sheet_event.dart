import 'package:equatable/equatable.dart';

abstract class BalanceSheetEvent extends Equatable {
  const BalanceSheetEvent();

  @override
  List<Object?> get props => [];
}

class LoadBalanceSheetData extends BalanceSheetEvent {
  const LoadBalanceSheetData();
}

class RefreshBalanceSheetData extends BalanceSheetEvent {
  const RefreshBalanceSheetData();
}

class ToggleItemExpansion extends BalanceSheetEvent {
  final String itemId;

  const ToggleItemExpansion(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

class CreateNewEntry extends BalanceSheetEvent {
  const CreateNewEntry();
}

class SwitchTab extends BalanceSheetEvent {
  final int tabIndex;

  const SwitchTab(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}
