import 'package:equatable/equatable.dart';
import '../models/balance_sheet_item.dart';

abstract class BalanceSheetState extends Equatable {
  const BalanceSheetState();

  @override
  List<Object?> get props => [];
}

class BalanceSheetInitial extends BalanceSheetState {
  const BalanceSheetInitial();
}

class BalanceSheetLoading extends BalanceSheetState {
  const BalanceSheetLoading();
}

class BalanceSheetLoaded extends BalanceSheetState {
  final BalanceSheetSummary summary;
  final int selectedTabIndex;

  const BalanceSheetLoaded({
    required this.summary,
    this.selectedTabIndex = 0,
  });

  @override
  List<Object?> get props => [summary, selectedTabIndex];

  BalanceSheetLoaded copyWith({
    BalanceSheetSummary? summary,
    int? selectedTabIndex,
  }) {
    return BalanceSheetLoaded(
      summary: summary ?? this.summary,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
    );
  }
}

class BalanceSheetError extends BalanceSheetState {
  final String message;

  const BalanceSheetError(this.message);

  @override
  List<Object?> get props => [message];
}

class BalanceSheetNavigateToCreateEntry extends BalanceSheetState {
  const BalanceSheetNavigateToCreateEntry();
}
