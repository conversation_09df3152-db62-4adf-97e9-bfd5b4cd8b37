import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'balance_sheet_event.dart';
import 'balance_sheet_state.dart';
import '../models/balance_sheet_item.dart';

class BalanceSheetBloc extends Bloc<BalanceSheetEvent, BalanceSheetState> {
  BalanceSheetBloc() : super(const BalanceSheetInitial()) {
    on<LoadBalanceSheetData>(_onLoadBalanceSheetData);
    on<RefreshBalanceSheetData>(_onRefreshBalanceSheetData);
    on<ToggleItemExpansion>(_onToggleItemExpansion);
    on<CreateNewEntry>(_onCreateNewEntry);
    on<SwitchTab>(_onSwitchTab);
  }

  // Mock data for balance sheet items - replace with actual API calls
  final List<BalanceSheetItem> _mockLiabilityItems = [
    const BalanceSheetItem(
      id: 'capital',
      title: 'Capital',
      amount: 0.0,
      type: BalanceSheetItemType.capital,
      category: BalanceSheetCategory.liabilities,
      icon: Icons.account_balance,
      iconColor: Colors.blue,
    ),
    BalanceSheetItem(
      id: 'current_liability',
      title: 'Current Liability',
      amount: 0.0,
      type: BalanceSheetItemType.currentLiability,
      category: BalanceSheetCategory.liabilities,
      icon: Icons.credit_card,
      iconColor: Colors.orange,
      isExpandable: true,
      subItems: const [
        BalanceSheetSubItem(
          id: 'tax_payable',
          title: 'Tax Payable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'tcs_payable',
          title: 'Tcs Payable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'tds_payable',
          title: 'Tds Payable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'account_payable',
          title: 'Account Payable',
          amount: 0.0,
        ),
      ],
    ),
    const BalanceSheetItem(
      id: 'loans',
      title: 'Loans',
      amount: 0.0,
      type: BalanceSheetItemType.loans,
      category: BalanceSheetCategory.liabilities,
      icon: Icons.monetization_on,
      iconColor: Colors.red,
    ),
    const BalanceSheetItem(
      id: 'net_income',
      title: 'Net Income',
      amount: 22657.49,
      type: BalanceSheetItemType.netIncome,
      category: BalanceSheetCategory.liabilities,
      icon: Icons.trending_up,
      iconColor: Colors.green,
    ),
  ];

  final List<BalanceSheetItem> _mockAssetItems = [
    const BalanceSheetItem(
      id: 'fixed_assets',
      title: 'Fixed Assets',
      amount: 0.0,
      type: BalanceSheetItemType.fixedAssets,
      category: BalanceSheetCategory.assets,
      icon: Icons.business,
      iconColor: Colors.purple,
    ),
    const BalanceSheetItem(
      id: 'investments',
      title: 'Investments',
      amount: 0.0,
      type: BalanceSheetItemType.investments,
      category: BalanceSheetCategory.assets,
      icon: Icons.show_chart,
      iconColor: Colors.indigo,
    ),
    BalanceSheetItem(
      id: 'current_assets',
      title: 'Current Assets',
      amount: 32681.07,
      type: BalanceSheetItemType.currentAssets,
      category: BalanceSheetCategory.assets,
      icon: Icons.account_balance_wallet,
      iconColor: Colors.green,
      isExpandable: true,
      subItems: const [
        BalanceSheetSubItem(
          id: 'tax_receivable',
          title: 'Tax Receivable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'tcs_receivable',
          title: 'Tcs Receivable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'tds_receivable',
          title: 'Tds Receivable',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'cash_in_hand',
          title: 'Cash In Hand',
          amount: 235.0,
        ),
        BalanceSheetSubItem(
          id: 'cash_in_bank',
          title: 'Cash In Bank',
          amount: 0.0,
        ),
        BalanceSheetSubItem(
          id: 'accounts_receivables',
          title: 'Accounts Receivables',
          amount: 10000.0,
        ),
        BalanceSheetSubItem(
          id: 'inventory_in_hand',
          title: 'Inventory In Hand',
          amount: 22446.07,
        ),
      ],
    ),
    const BalanceSheetItem(
      id: 'loans_advance',
      title: 'Loans Advance',
      amount: 0.0,
      type: BalanceSheetItemType.loansAdvance,
      category: BalanceSheetCategory.assets,
      icon: Icons.account_balance,
      iconColor: Colors.teal,
    ),
  ];

  Future<void> _onLoadBalanceSheetData(
    LoadBalanceSheetData event,
    Emitter<BalanceSheetState> emit,
  ) async {
    emit(const BalanceSheetLoading());

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      final summary = _calculateSummary();
      emit(BalanceSheetLoaded(summary: summary));
    } catch (e) {
      emit(
        BalanceSheetError('Failed to load balance sheet data: ${e.toString()}'),
      );
    }
  }

  Future<void> _onRefreshBalanceSheetData(
    RefreshBalanceSheetData event,
    Emitter<BalanceSheetState> emit,
  ) async {
    try {
      // For refresh, show loading briefly
      emit(const BalanceSheetLoading());
      await Future.delayed(const Duration(milliseconds: 300));

      final summary = _calculateSummary();
      emit(BalanceSheetLoaded(summary: summary));
    } catch (e) {
      emit(
        BalanceSheetError(
          'Failed to refresh balance sheet data: ${e.toString()}',
        ),
      );
    }
  }

  void _onToggleItemExpansion(
    ToggleItemExpansion event,
    Emitter<BalanceSheetState> emit,
  ) {
    final currentState = state;
    if (currentState is BalanceSheetLoaded) {
      // Toggle expansion for the specific item
      final updatedLiabilityItems = _toggleItemInList(
        currentState.summary.liabilityItems,
        event.itemId,
      );
      final updatedAssetItems = _toggleItemInList(
        currentState.summary.assetItems,
        event.itemId,
      );

      final updatedSummary = BalanceSheetSummary(
        totalLiabilities: currentState.summary.totalLiabilities,
        totalAssets: currentState.summary.totalAssets,
        liabilityItems: updatedLiabilityItems,
        assetItems: updatedAssetItems,
        lastUpdated: currentState.summary.lastUpdated,
      );

      emit(currentState.copyWith(summary: updatedSummary));
    }
  }

  List<BalanceSheetItem> _toggleItemInList(
    List<BalanceSheetItem> items,
    String itemId,
  ) {
    return items.map((item) {
      if (item.id == itemId && item.isExpandable) {
        return item.copyWith(isExpanded: !item.isExpanded);
      }
      return item;
    }).toList();
  }

  void _onCreateNewEntry(
    CreateNewEntry event,
    Emitter<BalanceSheetState> emit,
  ) {
    // Emit navigation state to trigger navigation to Create Entry screen
    emit(const BalanceSheetNavigateToCreateEntry());

    // Return to previous state after navigation trigger
    final currentState = state;
    if (currentState is BalanceSheetLoaded) {
      emit(currentState);
    }
  }

  void _onSwitchTab(SwitchTab event, Emitter<BalanceSheetState> emit) {
    final currentState = state;
    if (currentState is BalanceSheetLoaded) {
      emit(currentState.copyWith(selectedTabIndex: event.tabIndex));
    }
  }

  BalanceSheetSummary _calculateSummary() {
    final totalLiabilities = _mockLiabilityItems.fold<double>(
      0.0,
      (sum, item) => sum + item.amount,
    );

    final totalAssets = _mockAssetItems.fold<double>(
      0.0,
      (sum, item) => sum + item.amount,
    );

    return BalanceSheetSummary(
      totalLiabilities: totalLiabilities,
      totalAssets: totalAssets,
      liabilityItems: _mockLiabilityItems,
      assetItems: _mockAssetItems,
      lastUpdated: DateTime.now(),
    );
  }
}
