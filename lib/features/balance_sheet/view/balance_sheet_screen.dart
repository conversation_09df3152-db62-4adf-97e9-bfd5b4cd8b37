import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/balance_sheet_bloc.dart';
import '../bloc/balance_sheet_event.dart';
import '../bloc/balance_sheet_state.dart';
import '../models/balance_sheet_item.dart';
import '../widgets/balance_sheet_widgets.dart';

class BalanceSheetScreen extends StatelessWidget {
  const BalanceSheetScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          BalanceSheetBloc()..add(const LoadBalanceSheetData()),
      child: const BalanceSheetView(),
    );
  }
}

class BalanceSheetView extends StatefulWidget {
  const BalanceSheetView({super.key});

  @override
  State<BalanceSheetView> createState() => _BalanceSheetViewState();
}

class _BalanceSheetViewState extends State<BalanceSheetView>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: BlocListener<BalanceSheetBloc, BalanceSheetState>(
        listener: (context, state) {
          if (state is BalanceSheetNavigateToCreateEntry) {
            // Handle navigation to create entry screen
            // For now, show a snackbar
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: AppText(
                  'Create New Entry feature coming soon!',
                  color: Colors.white,
                  fontSize: 14,
                ),
                backgroundColor: Color(0xFF5A67D8),
                duration: Duration(seconds: 2),
              ),
            );
          }
        },
        child: BlocBuilder<BalanceSheetBloc, BalanceSheetState>(
          builder: (context, state) {
            if (state is BalanceSheetLoading) {
              return _buildLoadingState();
            } else if (state is BalanceSheetError) {
              return _buildErrorState(state.message);
            } else if (state is BalanceSheetLoaded) {
              return _buildLoadedState(context, state);
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Platform.isIOS ? Icons.arrow_back_ios_new_rounded : Icons.arrow_back,
          color: Colors.black87,
        ),
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
      ),
      title: const AppText(
        'Balance Sheet (As of Today)',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.picture_as_pdf, color: Colors.orange),
          onPressed: () {
            HapticFeedback.lightImpact();
            // Handle PDF export
          },
        ),
        IconButton(
          icon: const Icon(Icons.table_chart, color: Colors.green),
          onPressed: () {
            HapticFeedback.lightImpact();
            // Handle Excel export
          },
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFF5A67D8)),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          AppText(
            message,
            fontSize: 16,
            color: Colors.red,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<BalanceSheetBloc>().add(
                const LoadBalanceSheetData(),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5A67D8),
              foregroundColor: Colors.white,
            ),
            child: const AppText('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, BalanceSheetLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<BalanceSheetBloc>().add(const RefreshBalanceSheetData());
      },
      child: SafeArea(
        child: Column(
          children: [
            _buildInfoBanner(state.summary),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLiabilitiesTab(context, state),
                  _buildAssetsTab(context, state),
                ],
              ),
            ),
            _buildCreateNewEntryButton(context),
            _buildTotalSummary(state.summary),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoBanner(BalanceSheetSummary summary) {
    final formatter = DateFormat('dd MMM yyyy, hh:mm a');
    final formattedDate = formatter.format(summary.lastUpdated);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF8E1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFFFE082)),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Color(0xFFFF8F00), size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: AppText(
              'Balance sheet is updated once per day. Last updated at: $formattedDate',
              fontSize: 12,
              color: const Color(0xFFE65100),
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF5A67D8),
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: const Color(0xFF5A67D8),
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        dividerColor: Colors.transparent,
        labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        onTap: (index) {
          HapticFeedback.lightImpact();
          context.read<BalanceSheetBloc>().add(SwitchTab(index));
        },
        tabs: const [
          Tab(text: 'Liabilities'),
          Tab(text: 'Assets'),
        ],
      ),
    );
  }

  Widget _buildLiabilitiesTab(BuildContext context, BalanceSheetLoaded state) {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: state.summary.liabilityItems.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: BalanceSheetItemWidget(
                  item: state.summary.liabilityItems[index],
                  onTap: () {
                    HapticFeedback.lightImpact();
                    if (state.summary.liabilityItems[index].isExpandable) {
                      context.read<BalanceSheetBloc>().add(
                        ToggleItemExpansion(
                          state.summary.liabilityItems[index].id,
                        ),
                      );
                    }
                    if (state.summary.liabilityItems[index].isExpanded) {
                      context.read<BalanceSheetBloc>().add(
                        ToggleItemExpansion(
                          state.summary.liabilityItems[index].id,
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAssetsTab(BuildContext context, BalanceSheetLoaded state) {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: state.summary.assetItems.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: BalanceSheetItemWidget(
                  item: state.summary.assetItems[index],
                  onTap: () {
                    HapticFeedback.lightImpact();
                    if (state.summary.assetItems[index].isExpandable) {
                      context.read<BalanceSheetBloc>().add(
                        ToggleItemExpansion(state.summary.assetItems[index].id),
                      );
                    }
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCreateNewEntryButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: () {
          HapticFeedback.lightImpact();
          context.read<BalanceSheetBloc>().add(const CreateNewEntry());
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF5A67D8),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.add, size: 20),
        label: const AppText(
          'Create New Entry',
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildTotalSummary(BalanceSheetSummary summary) {
    final formatter = NumberFormat('#,##0.00');

    return AnimatedBuilder(
      animation: _tabController,
      builder: (context, child) {
        final isLiabilitiesTab = _tabController.index == 0;
        final total = isLiabilitiesTab
            ? summary.totalLiabilities
            : summary.totalAssets;
        final label = isLiabilitiesTab ? 'Total Liabilities' : 'Total Assets';

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(color: Color(0xFF5A67D8)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: AppText(
                  label,
                  key: ValueKey(label),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: AppText(
                  '₹ ${formatter.format(total)}',
                  key: ValueKey(total),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
