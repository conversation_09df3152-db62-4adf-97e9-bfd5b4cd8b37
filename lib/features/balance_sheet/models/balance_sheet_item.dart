import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

enum BalanceSheetItemType {
  capital,
  currentLiability,
  loans,
  netIncome,
  fixedAssets,
  investments,
  currentAssets,
  loansAdvance,
}

enum BalanceSheetCategory {
  liabilities,
  assets,
}

class BalanceSheetItem extends Equatable {
  final String id;
  final String title;
  final double amount;
  final BalanceSheetItemType type;
  final BalanceSheetCategory category;
  final IconData? icon;
  final Color? iconColor;
  final List<BalanceSheetSubItem>? subItems;
  final bool isExpandable;
  final bool isExpanded;

  const BalanceSheetItem({
    required this.id,
    required this.title,
    required this.amount,
    required this.type,
    required this.category,
    this.icon,
    this.iconColor,
    this.subItems,
    this.isExpandable = false,
    this.isExpanded = false,
  });

  BalanceSheetItem copyWith({
    String? id,
    String? title,
    double? amount,
    BalanceSheetItemType? type,
    BalanceSheetCategory? category,
    IconData? icon,
    Color? iconColor,
    List<BalanceSheetSubItem>? subItems,
    bool? isExpandable,
    bool? isExpanded,
  }) {
    return BalanceSheetItem(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      subItems: subItems ?? this.subItems,
      isExpandable: isExpandable ?? this.isExpandable,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        amount,
        type,
        category,
        icon,
        iconColor,
        subItems,
        isExpandable,
        isExpanded,
      ];
}

class BalanceSheetSubItem extends Equatable {
  final String id;
  final String title;
  final double amount;

  const BalanceSheetSubItem({
    required this.id,
    required this.title,
    required this.amount,
  });

  @override
  List<Object?> get props => [id, title, amount];
}

class BalanceSheetSummary extends Equatable {
  final double totalLiabilities;
  final double totalAssets;
  final List<BalanceSheetItem> liabilityItems;
  final List<BalanceSheetItem> assetItems;
  final DateTime lastUpdated;

  const BalanceSheetSummary({
    required this.totalLiabilities,
    required this.totalAssets,
    required this.liabilityItems,
    required this.assetItems,
    required this.lastUpdated,
  });

  bool get isBalanced => totalLiabilities == totalAssets;

  @override
  List<Object?> get props => [
        totalLiabilities,
        totalAssets,
        liabilityItems,
        assetItems,
        lastUpdated,
      ];
}
