import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class CashBankItem extends Equatable {
  final String id;
  final String title;
  final double amount;
  final String type; // 'cash' or 'bank'
  final IconData? icon;
  final Color? iconColor;
  final bool isActive;

  const CashBankItem({
    required this.id,
    required this.title,
    required this.amount,
    required this.type,
    this.icon,
    this.iconColor,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [id, title, amount, type, icon, iconColor, isActive];

  factory CashBankItem.fromJson(Map<String, dynamic> json) {
    return CashBankItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      type: json['type'] ?? 'cash',
      icon: _getIconFromString(json['icon']),
      iconColor: _getColorFromString(json['iconColor']),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'amount': amount,
      'type': type,
      'icon': _getStringFromIcon(icon),
      'iconColor': _getStringFromColor(iconColor),
      'isActive': isActive,
    };
  }

  static IconData? _getIconFromString(String? iconName) {
    if (iconName == null) return null;
    switch (iconName) {
      case 'account_balance_wallet':
        return Icons.account_balance_wallet;
      case 'account_balance':
        return Icons.account_balance;
      case 'credit_card':
        return Icons.credit_card;
      case 'money':
        return Icons.money;
      default:
        return Icons.account_balance_wallet;
    }
  }

  static Color? _getColorFromString(String? colorName) {
    if (colorName == null) return null;
    switch (colorName) {
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  static String? _getStringFromIcon(IconData? icon) {
    if (icon == null) return null;
    return 'account_balance_wallet';
  }

  static String? _getStringFromColor(Color? color) {
    if (color == null) return null;
    return 'blue';
  }

  CashBankItem copyWith({
    String? id,
    String? title,
    double? amount,
    String? type,
    IconData? icon,
    Color? iconColor,
    bool? isActive,
  }) {
    return CashBankItem(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      isActive: isActive ?? this.isActive,
    );
  }
}

class CashBankSummary extends Equatable {
  final double totalCashBalance;
  final double totalBankBalance;
  final double totalBalance;
  final List<CashBankItem> cashItems;
  final List<CashBankItem> bankItems;
  final double unlinkedPayments;

  const CashBankSummary({
    required this.totalCashBalance,
    required this.totalBankBalance,
    required this.totalBalance,
    required this.cashItems,
    required this.bankItems,
    this.unlinkedPayments = 0.0,
  });

  @override
  List<Object?> get props => [
        totalCashBalance,
        totalBankBalance,
        totalBalance,
        cashItems,
        bankItems,
        unlinkedPayments,
      ];

  factory CashBankSummary.fromJson(Map<String, dynamic> json) {
    return CashBankSummary(
      totalCashBalance: (json['totalCashBalance'] ?? 0.0).toDouble(),
      totalBankBalance: (json['totalBankBalance'] ?? 0.0).toDouble(),
      totalBalance: (json['totalBalance'] ?? 0.0).toDouble(),
      cashItems: (json['cashItems'] as List<dynamic>?)
              ?.map((item) => CashBankItem.fromJson(item))
              .toList() ??
          [],
      bankItems: (json['bankItems'] as List<dynamic>?)
              ?.map((item) => CashBankItem.fromJson(item))
              .toList() ??
          [],
      unlinkedPayments: (json['unlinkedPayments'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCashBalance': totalCashBalance,
      'totalBankBalance': totalBankBalance,
      'totalBalance': totalBalance,
      'cashItems': cashItems.map((item) => item.toJson()).toList(),
      'bankItems': bankItems.map((item) => item.toJson()).toList(),
      'unlinkedPayments': unlinkedPayments,
    };
  }

  CashBankSummary copyWith({
    double? totalCashBalance,
    double? totalBankBalance,
    double? totalBalance,
    List<CashBankItem>? cashItems,
    List<CashBankItem>? bankItems,
    double? unlinkedPayments,
  }) {
    return CashBankSummary(
      totalCashBalance: totalCashBalance ?? this.totalCashBalance,
      totalBankBalance: totalBankBalance ?? this.totalBankBalance,
      totalBalance: totalBalance ?? this.totalBalance,
      cashItems: cashItems ?? this.cashItems,
      bankItems: bankItems ?? this.bankItems,
      unlinkedPayments: unlinkedPayments ?? this.unlinkedPayments,
    );
  }
}
