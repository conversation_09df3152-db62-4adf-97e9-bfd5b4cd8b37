import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'cash_bank_event.dart';
import 'cash_bank_state.dart';
import '../models/cash_bank_item.dart';

class CashBankBloc extends Bloc<CashBankEvent, CashBankState> {
  CashBankBloc() : super(const CashBankInitial()) {
    on<LoadCashBankData>(_onLoadCashBankData);
    on<RefreshCashBankData>(_onRefreshCashBankData);
    on<AddNewBank>(_onAddNewBank);
    on<AdjustBalance>(_onAdjustBalance);
    on<ViewCashDetails>(_onViewCashDetails);
    on<FilterCashBankData>(_onFilterCashBankData);
  }

  // Mock data for cash and bank items - replace with actual API calls
  final List<CashBankItem> _mockCashItems = [
    const CashBankItem(
      id: 'cash_in_hand',
      title: 'Cash in Hand',
      amount: 235.0,
      type: 'cash',
      icon: Icons.account_balance_wallet,
      iconColor: Colors.green,
    ),
  ];

  final List<CashBankItem> _mockBankItems = [
    // Currently empty as shown in the design
  ];

  Future<void> _onLoadCashBankData(
    LoadCashBankData event,
    Emitter<CashBankState> emit,
  ) async {
    emit(const CashBankLoading());

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      final summary = _calculateSummary();
      emit(CashBankLoaded(summary: summary));
    } catch (e) {
      emit(CashBankError('Failed to load cash and bank data: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshCashBankData(
    RefreshCashBankData event,
    Emitter<CashBankState> emit,
  ) async {
    try {
      // For refresh, show loading briefly
      emit(const CashBankLoading());
      await Future.delayed(const Duration(milliseconds: 300));

      final summary = _calculateSummary();
      emit(CashBankLoaded(summary: summary));
    } catch (e) {
      emit(
        CashBankError('Failed to refresh cash and bank data: ${e.toString()}'),
      );
    }
  }

  Future<void> _onAddNewBank(
    AddNewBank event,
    Emitter<CashBankState> emit,
  ) async {
    // Emit navigation state to trigger navigation to Add Bank screen
    emit(const CashBankNavigateToAddBank());

    // Return to previous state after navigation trigger
    final summary = _calculateSummary();
    emit(CashBankLoaded(summary: summary));
  }

  Future<void> _onAdjustBalance(
    AdjustBalance event,
    Emitter<CashBankState> emit,
  ) async {
    // Handle adjust balance logic
    print('Adjust balance requested');
    // This would typically open a dialog or navigate to a balance adjustment screen
  }

  Future<void> _onViewCashDetails(
    ViewCashDetails event,
    Emitter<CashBankState> emit,
  ) async {
    // Handle view cash details logic
    print('View cash details requested');
    // This would typically navigate to a detailed cash view
  }

  Future<void> _onFilterCashBankData(
    FilterCashBankData event,
    Emitter<CashBankState> emit,
  ) async {
    final currentState = state;
    if (currentState is CashBankLoaded) {
      emit(currentState.copyWith(selectedFilter: event.filter));
    }
  }

  CashBankSummary _calculateSummary() {
    final totalCash = _mockCashItems.fold<double>(
      0.0,
      (sum, item) => sum + item.amount,
    );

    final totalBank = _mockBankItems.fold<double>(
      0.0,
      (sum, item) => sum + item.amount,
    );

    final totalBalance = totalCash + totalBank;

    return CashBankSummary(
      totalCashBalance: totalCash,
      totalBankBalance: totalBank,
      totalBalance: totalBalance,
      cashItems: _mockCashItems,
      bankItems: _mockBankItems,
      unlinkedPayments: 0.0, // Mock data shows 0.0
    );
  }
}
