import 'package:equatable/equatable.dart';
import '../models/cash_bank_item.dart';

abstract class CashBankState extends Equatable {
  const CashBankState();

  @override
  List<Object?> get props => [];
}

class CashBankInitial extends CashBankState {
  const CashBankInitial();
}

class CashBankLoading extends CashBankState {
  const CashBankLoading();
}

class CashBankLoaded extends CashBankState {
  final CashBankSummary summary;
  final String selectedFilter;

  const CashBankLoaded({
    required this.summary,
    this.selectedFilter = 'All',
  });

  @override
  List<Object?> get props => [summary, selectedFilter];

  CashBankLoaded copyWith({
    CashBankSummary? summary,
    String? selectedFilter,
  }) {
    return CashBankLoaded(
      summary: summary ?? this.summary,
      selectedFilter: selectedFilter ?? this.selectedFilter,
    );
  }
}

class CashBankError extends CashBankState {
  final String message;

  const CashBankError(this.message);

  @override
  List<Object?> get props => [message];
}
