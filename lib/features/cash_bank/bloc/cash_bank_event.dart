import 'package:equatable/equatable.dart';

abstract class CashBankEvent extends Equatable {
  const CashBankEvent();

  @override
  List<Object?> get props => [];
}

class LoadCashBankData extends CashBankEvent {
  const LoadCashBankData();
}

class RefreshCashBankData extends CashBankEvent {
  const RefreshCashBankData();
}

class AddNewBank extends CashBankEvent {
  const AddNewBank();
}

class AdjustBalance extends CashBankEvent {
  const AdjustBalance();
}

class ViewCashDetails extends CashBankEvent {
  const ViewCashDetails();
}

class FilterCashBankData extends CashBankEvent {
  final String filter;

  const FilterCashBankData(this.filter);

  @override
  List<Object?> get props => [filter];
}
