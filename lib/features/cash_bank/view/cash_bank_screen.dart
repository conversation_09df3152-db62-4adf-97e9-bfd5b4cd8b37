import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/cash_bank_bloc.dart';
import '../bloc/cash_bank_event.dart';
import '../bloc/cash_bank_state.dart';
import '../models/cash_bank_item.dart';
import '../widget/adjust_balance_bottom_sheet.dart';
import '../widget/amount_range_filter.dart';
import '../widget/balance_card.dart';
import '../widget/cash_bank_error.dart';
import '../widget/cash_bank_widgets.dart';

class CashBankScreen extends StatelessWidget {
  const CashBankScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CashBankBloc()..add(const LoadCashBankData()),
      child: const CashBankView(),
    );
  }
}

class CashBankView extends StatefulWidget {
  const CashBankView({super.key});

  @override
  State<CashBankView> createState() => _CashBankViewState();
}

class _CashBankViewState extends State<CashBankView>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _balanceCountController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _balanceCountAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _balanceCountController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Initialize animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _balanceCountAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _balanceCountController,
        curve: Curves.easeOutQuart,
      ),
    );

    // Start animations with staggered delays
    _startAnimations();
  }

  void _startAnimations() {
    _fadeController.forward();

    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 600), () {
      _balanceCountController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _balanceCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Hero(
          tag: 'cash_bank_title',
          child: Material(
            color: Colors.transparent,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: const AppText(
                'Cash & Bank',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        ),
        centerTitle: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.blue),
            onPressed: () {
              // Handle filter action
              _showFilterOptions(context);
            },
          ),
        ],
      ),
      body: BlocBuilder<CashBankBloc, CashBankState>(
        builder: (context, state) {
          if (state is CashBankLoading) {
            return buildShimmerLoading();
          } else if (state is CashBankError) {
            return CashBankErrorWidget(state: state);
          } else if (state is CashBankLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<CashBankBloc>().add(const RefreshCashBankData());
              },
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Total Balance Summary Card with scale animation
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildTotalBalanceCard(context, state.summary),
                        ),
                        const SizedBox(height: 24),

                        // Cash in Hand Section with staggered animation
                        _buildAnimatedSection(
                          delay: 800,
                          child: _buildCashSection(context, state.summary),
                        ),
                        const SizedBox(height: 24),

                        // Bank/Online Section with staggered animation
                        _buildAnimatedSection(
                          delay: 1000,
                          child: _buildBankSection(context, state.summary),
                        ),
                        const SizedBox(
                          height: 100,
                        ), // Space for floating button
                      ],
                    ),
                  ),
                ),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: SlideTransition(
        position: Tween<Offset>(begin: const Offset(0, 2), end: Offset.zero)
            .animate(
              CurvedAnimation(
                parent: _slideController,
                curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
              ),
            ),
        child: _buildAdjustBalanceButton(context),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  // Helper method to create staggered animations for sections
  Widget _buildAnimatedSection({required int delay, required Widget child}) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }

  Widget _buildTotalBalanceCard(BuildContext context, CashBankSummary summary) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const AppText(
            'Total Cash & Bank Balance',
            fontSize: 14,
            color: Colors.grey,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _balanceCountAnimation,
            builder: (context, child) {
              final animatedValue =
                  summary.totalBalance * _balanceCountAnimation.value;
              return AppText(
                '₹ ${animatedValue.toStringAsFixed(0)}',
                fontSize: 32,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                textAlign: TextAlign.center,
              );
            },
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () {
              // Handle view details
              _showDetailsDialog(context, summary);
            },
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppText(
                  'View Details',
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(width: 4),
                Icon(Icons.arrow_forward, color: Colors.blue, size: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCashSection(BuildContext context, CashBankSummary summary) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (summary.cashItems.isNotEmpty) ...[
          ...summary.cashItems.map((item) => _buildCashItem(context, item)),
        ] else
          buildEmptyState('No cash entries found'),
      ],
    );
  }

  Widget _buildCashItem(BuildContext context, CashBankItem item) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween<double>(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * value),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    item.title,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black54,
                  ),
                  const SizedBox(height: 4),
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1000),
                    tween: Tween<double>(begin: 0.0, end: item.amount),
                    builder: (context, value, child) {
                      return AppText(
                        '₹ ${value.toStringAsFixed(0)}',
                        fontSize: 14,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      );
                    },
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                context.read<CashBankBloc>().add(const ViewCashDetails());
              },
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 200),
                tween: Tween<double>(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.rotate(
                    angle: value * 0.1,
                    child: const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.blue,
                      size: 16,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankSection(BuildContext context, CashBankSummary summary) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const AppText(
              'Bank/Online',
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black54,
            ),
            GestureDetector(
              onTap: () {
                context.read<CashBankBloc>().add(const AddNewBank());
              },
              child: TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 1500),
                tween: Tween<double>(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale:
                        1.0 +
                        (0.05 * (0.5 + 0.5 * math.sin(math.pi * 2 * value))),
                    child: child,
                  );
                },
                child: const Row(
                  children: [
                    Icon(Icons.add, color: Colors.blue, size: 16),
                    SizedBox(width: 4),
                    AppText(
                      'Add New Bank',
                      fontSize: 13,
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Unlinked Payments
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(5),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const AppText(
                'Unlinked Payments',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              AppText(
                '₹ ${summary.unlinkedPayments.toStringAsFixed(1)}',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdjustBalanceButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton(
        onPressed: () {
          _showAdjustBalanceBottomSheet(context);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF5A67D8),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 4,
        ),
        child: const AppText(
          'Adjust Balance',
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    String selectedDateFilter = 'All Time';
    String selectedAccountType = 'All';
    String selectedStatus = 'Active account';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: Navigator.of(context),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header Section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Color(0xFFE5E5E5), width: 1),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const AppText(
                        'Filter Options',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close, color: Colors.grey),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Scrollable Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Account Status Filter
                        _buildFilterSection(
                          title: 'Account Status',
                          options: [
                            'Active account',
                            'Deleted accounts',
                            'All',
                          ],
                          selectedValue: selectedStatus,
                          onChanged: (value) {
                            setState(() {
                              selectedStatus = value;
                            });
                          },
                        ),
                        const SizedBox(height: 24),

                        // Date Range Filter
                        _buildFilterSection(
                          title: 'Date Range',
                          options: [
                            'Today',
                            'This Week',
                            'This Month',
                            'Last 3 Months',
                            'All Time',
                          ],
                          selectedValue: selectedDateFilter,
                          onChanged: (value) {
                            setState(() {
                              selectedDateFilter = value;
                            });
                          },
                        ),
                        const SizedBox(height: 24),

                        // Account Type Filter
                        _buildFilterSection(
                          title: 'Account Type',
                          options: ['Cash', 'Bank', 'All'],
                          selectedValue: selectedAccountType,
                          onChanged: (value) {
                            setState(() {
                              selectedAccountType = value;
                            });
                          },
                        ),
                        const SizedBox(height: 24),

                        // Amount Range Filter
                        buildAmountRangeFilter(),
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE5E5E5), width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            // Reset all filters
                            setState(() {
                              selectedDateFilter = 'All Time';
                              selectedAccountType = 'All';
                              selectedStatus = 'Active account';
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: const BorderSide(color: Color(0xFF5A67D8)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const AppText(
                            'Clear All',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF5A67D8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            // Apply filters
                            context.read<CashBankBloc>().add(
                              FilterCashBankData(
                                '$selectedStatus|$selectedDateFilter|$selectedAccountType',
                              ),
                            );
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF5A67D8),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const AppText(
                            'Apply Filter',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection({
    required String title,
    required List<String> options,
    required String selectedValue,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          title,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 12),
        ...options.map(
          (option) => _buildFilterOption(
            option,
            selectedValue == option,
            () => onChanged(option),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterOption(String title, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 200),
        tween: Tween<double>(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: isSelected ? 0.98 + (0.02 * value) : 1.0,
            child: child,
          );
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                : Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.transparent,
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF5A67D8)
                        : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, size: 12, color: Colors.white)
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: AppText(
                  title,
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAdjustBalanceBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: Navigator.of(context),
      ),
      builder: (context) => const AdjustBalanceBottomSheet(),
    );
  }

  void _showDetailsDialog(BuildContext context, CashBankSummary summary) {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
        backgroundColor: Colors.white,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: const Color(0xFF5A67D8),
                        borderRadius: BorderRadius.circular(28),
                      ),
                      child: const Icon(
                        Icons.account_balance_wallet_outlined,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 12),
                    const AppText(
                      'Balance Overview',
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    const SizedBox(height: 4),
                    const AppText(
                      'Current account balances',
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Cash Balance Card
                    buildBalanceCard(
                      icon: Icons.payments_outlined,
                      title: 'Cash Balance',
                      amount: summary.totalCashBalance,
                      color: Colors.green,
                    ),
                    const SizedBox(height: 12),

                    // Bank Balance Card
                    buildBalanceCard(
                      icon: Icons.account_balance_outlined,
                      title: 'Bank Balance',
                      amount: summary.totalBankBalance,
                      color: Colors.blue,
                    ),
                    const SizedBox(height: 16),

                    // Total Balance Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF5A67D8).withValues(alpha: 0.1),
                            const Color(0xFF5A67D8).withValues(alpha: 0.15),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF5A67D8).withValues(alpha: 0.3),
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFF5A67D8),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.account_balance_wallet,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const AppText(
                                  'Total Balance',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                                const SizedBox(height: 4),
                                AppText(
                                  '₹ ${summary.totalBalance.toStringAsFixed(0)}',
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                  color: const Color(0xFF5A67D8),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: summary.totalBalance >= 0
                                  ? Colors.green.withValues(alpha: 0.1)
                                  : Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  summary.totalBalance >= 0
                                      ? Icons.trending_up
                                      : Icons.trending_down,
                                  size: 12,
                                  color: summary.totalBalance >= 0
                                      ? Colors.green[700]
                                      : Colors.red[700],
                                ),
                                const SizedBox(width: 4),
                                AppText(
                                  summary.totalBalance >= 0
                                      ? 'Positive'
                                      : 'Negative',
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: summary.totalBalance >= 0
                                      ? Colors.green[700]!
                                      : Colors.red[700]!,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Action Button
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5A67D8),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                      shadowColor: const Color(
                        0xFF5A67D8,
                      ).withValues(alpha: 0.3),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          size: 18,
                          color: Colors.white,
                        ),
                        SizedBox(width: 8),
                        AppText(
                          'Got it',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
