import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/widgets/app_text.dart';
import '../bloc/cash_bank_bloc.dart';
import '../bloc/cash_bank_event.dart';
import '../bloc/cash_bank_state.dart';

class CashBankErrorWidget extends StatelessWidget {
  final CashBankError state;
  const CashBankErrorWidget({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          AppText(
            state.message,
            fontSize: 16,
            color: Colors.red,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<CashBankBloc>().add(const LoadCashBankData());
            },
            child: const AppText('Retry'),
          ),
        ],
      ),
    );
  }
}
