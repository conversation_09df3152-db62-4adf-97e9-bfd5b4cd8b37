import 'package:flutter/material.dart';

import '../../../core/widgets/app_text.dart';

Widget buildBalanceCard({
  required IconData icon,
  required String title,
  required double amount,
  required MaterialColor color,
}) {
  return Container(
    width: double.infinity,
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: color.withValues(alpha: 0.05),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
    ),
    child: Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Icon(icon, color: color[700], size: 18),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                title,
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 4),
              AppText(
                '₹ ${amount.toStringAsFixed(0)}',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color[700]!,
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
