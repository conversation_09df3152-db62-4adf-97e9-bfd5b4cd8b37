import 'package:equatable/equatable.dart';

enum PartyReportsStatus { initial, loading, loaded, error }

class PartyReportItem extends Equatable {
  final String id;
  final String title;
  final String description;
  final String type;

  const PartyReportItem({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
  });

  @override
  List<Object?> get props => [id, title, description, type];
}

class PartyReportsState extends Equatable {
  final PartyReportsStatus status;
  final List<PartyReportItem> reportItems;
  final String? errorMessage;

  const PartyReportsState({
    this.status = PartyReportsStatus.initial,
    this.reportItems = const [],
    this.errorMessage,
  });

  PartyReportsState copyWith({
    PartyReportsStatus? status,
    List<PartyReportItem>? reportItems,
    String? errorMessage,
  }) {
    return PartyReportsState(
      status: status ?? this.status,
      reportItems: reportItems ?? this.reportItems,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, reportItems, errorMessage];
}
