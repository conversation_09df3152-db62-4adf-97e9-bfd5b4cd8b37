import 'package:flutter_bloc/flutter_bloc.dart';
import 'party_reports_event.dart';
import 'party_reports_state.dart';

class PartyReportsBloc extends Bloc<PartyReportsEvent, PartyReportsState> {
  PartyReportsBloc() : super(const PartyReportsState()) {
    on<LoadPartyReports>(_onLoadPartyReports);
    on<PartyReportItemTapped>(_onPartyReportItemTapped);
    on<RefreshPartyReports>(_onRefreshPartyReports);
  }

  Future<void> _onLoadPartyReports(
    LoadPartyReports event,
    Emitter<PartyReportsState> emit,
  ) async {
    emit(state.copyWith(status: PartyReportsStatus.loading));

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Sample report items matching the image design
      final reportItems = [
        const PartyReportItem(
          id: 'party_report_by_item',
          title: 'Party Report by Item',
          description: 'Find parties which bought or sold a particular item',
          type: 'party_by_item',
        ),
        const PartyReportItem(
          id: 'party_statement_ledger',
          title: 'Party Statement (Ledger)',
          description: 'View detailed party transaction history and balance',
          type: 'party_statement',
        ),
        const PartyReportItem(
          id: 'party_wise_outstanding',
          title: 'Party wise outstanding',
          description: 'View outstanding amounts for all parties',
          type: 'party_outstanding',
        ),
      ];

      emit(state.copyWith(
        status: PartyReportsStatus.loaded,
        reportItems: reportItems,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: PartyReportsStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onPartyReportItemTapped(
    PartyReportItemTapped event,
    Emitter<PartyReportsState> emit,
  ) {
    // Handle navigation to specific report screens
    // This will be handled in the UI layer
  }

  Future<void> _onRefreshPartyReports(
    RefreshPartyReports event,
    Emitter<PartyReportsState> emit,
  ) async {
    add(const LoadPartyReports());
  }
}
