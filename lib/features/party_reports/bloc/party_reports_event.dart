import 'package:equatable/equatable.dart';

abstract class PartyReportsEvent extends Equatable {
  const PartyReportsEvent();

  @override
  List<Object?> get props => [];
}

class LoadPartyReports extends PartyReportsEvent {
  const LoadPartyReports();
}

class PartyReportItemTapped extends PartyReportsEvent {
  final String reportType;

  const PartyReportItemTapped(this.reportType);

  @override
  List<Object?> get props => [reportType];
}

class RefreshPartyReports extends PartyReportsEvent {
  const RefreshPartyReports();
}
