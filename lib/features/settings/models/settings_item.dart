import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

enum SettingsItemType {
  toggle,
  navigation,
  section,
  selection,
}

class SettingsItem extends Equatable {
  final String id;
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;
  final SettingsItemType type;
  final bool? isEnabled;
  final bool? hasArrow;
  final VoidCallback? onTap;
  final Function(bool)? onToggle;
  final List<String>? options;
  final String? selectedOption;

  const SettingsItem({
    required this.id,
    required this.title,
    this.subtitle,
    this.icon,
    this.iconColor,
    required this.type,
    this.isEnabled,
    this.hasArrow,
    this.onTap,
    this.onToggle,
    this.options,
    this.selectedOption,
  });

  SettingsItem copyWith({
    String? id,
    String? title,
    String? subtitle,
    IconData? icon,
    Color? iconColor,
    SettingsItemType? type,
    bool? isEnabled,
    bool? hasArrow,
    VoidCallback? onTap,
    Function(bool)? onToggle,
    List<String>? options,
    String? selectedOption,
  }) {
    return SettingsItem(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      type: type ?? this.type,
      isEnabled: isEnabled ?? this.isEnabled,
      hasArrow: hasArrow ?? this.hasArrow,
      onTap: onTap ?? this.onTap,
      onToggle: onToggle ?? this.onToggle,
      options: options ?? this.options,
      selectedOption: selectedOption ?? this.selectedOption,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        subtitle,
        icon,
        iconColor,
        type,
        isEnabled,
        hasArrow,
        options,
        selectedOption,
      ];
}

class SettingsSection extends Equatable {
  final String title;
  final List<SettingsItem> items;

  const SettingsSection({
    required this.title,
    required this.items,
  });

  SettingsSection copyWith({
    String? title,
    List<SettingsItem>? items,
  }) {
    return SettingsSection(
      title: title ?? this.title,
      items: items ?? this.items,
    );
  }

  @override
  List<Object?> get props => [title, items];
}
