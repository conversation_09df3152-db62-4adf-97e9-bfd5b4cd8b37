import 'package:equatable/equatable.dart';
import '../models/settings_item.dart';

abstract class SettingsState extends Equatable {
  const SettingsState();

  @override
  List<Object?> get props => [];
}

class SettingsInitial extends SettingsState {
  const SettingsInitial();
}

class SettingsLoading extends SettingsState {
  const SettingsLoading();
}

class SettingsLoaded extends SettingsState {
  final bool wholesalePriceEnabled;
  final bool partyWiseItemPriceEnabled;
  final bool barcodeScannerEnabled;
  final bool reminderForServiceEnabled;
  final bool serialNumberIMEIEnabled;
  final bool batchingEnabled;
  final String stockValueCalculationType;
  final List<SettingsSection> sections;

  const SettingsLoaded({
    required this.wholesalePriceEnabled,
    required this.partyWiseItemPriceEnabled,
    required this.barcodeScannerEnabled,
    required this.reminderForServiceEnabled,
    required this.serialNumberIMEIEnabled,
    required this.batchingEnabled,
    required this.stockValueCalculationType,
    required this.sections,
  });

  SettingsLoaded copyWith({
    bool? wholesalePriceEnabled,
    bool? partyWiseItemPriceEnabled,
    bool? barcodeScannerEnabled,
    bool? reminderForServiceEnabled,
    bool? serialNumberIMEIEnabled,
    bool? batchingEnabled,
    String? stockValueCalculationType,
    List<SettingsSection>? sections,
  }) {
    return SettingsLoaded(
      wholesalePriceEnabled: wholesalePriceEnabled ?? this.wholesalePriceEnabled,
      partyWiseItemPriceEnabled: partyWiseItemPriceEnabled ?? this.partyWiseItemPriceEnabled,
      barcodeScannerEnabled: barcodeScannerEnabled ?? this.barcodeScannerEnabled,
      reminderForServiceEnabled: reminderForServiceEnabled ?? this.reminderForServiceEnabled,
      serialNumberIMEIEnabled: serialNumberIMEIEnabled ?? this.serialNumberIMEIEnabled,
      batchingEnabled: batchingEnabled ?? this.batchingEnabled,
      stockValueCalculationType: stockValueCalculationType ?? this.stockValueCalculationType,
      sections: sections ?? this.sections,
    );
  }

  @override
  List<Object?> get props => [
        wholesalePriceEnabled,
        partyWiseItemPriceEnabled,
        barcodeScannerEnabled,
        reminderForServiceEnabled,
        serialNumberIMEIEnabled,
        batchingEnabled,
        stockValueCalculationType,
        sections,
      ];
}

class SettingsError extends SettingsState {
  final String message;

  const SettingsError(this.message);

  @override
  List<Object?> get props => [message];
}
