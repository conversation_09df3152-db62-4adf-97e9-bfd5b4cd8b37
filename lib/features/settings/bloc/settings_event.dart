import 'package:equatable/equatable.dart';

abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadSettings extends SettingsEvent {
  const LoadSettings();
}

class ToggleWholesalePrice extends SettingsEvent {
  final bool isEnabled;

  const ToggleWholesalePrice(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class TogglePartyWiseItemPrice extends SettingsEvent {
  final bool isEnabled;

  const TogglePartyWiseItemPrice(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleBarcodeScanner extends SettingsEvent {
  final bool isEnabled;

  const ToggleBarcodeScanner(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleReminderForService extends SettingsEvent {
  final bool isEnabled;

  const ToggleReminderForService(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleSerialNumberIMEI extends SettingsEvent {
  final bool isEnabled;

  const ToggleSerialNumberIMEI(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleBatching extends SettingsEvent {
  final bool isEnabled;

  const ToggleBatching(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class NavigateToAddRemoveColumns extends SettingsEvent {
  const NavigateToAddRemoveColumns();
}

class NavigateToGodownWarehouse extends SettingsEvent {
  const NavigateToGodownWarehouse();
}

class ChangeStockValueCalculation extends SettingsEvent {
  final String calculationType;

  const ChangeStockValueCalculation(this.calculationType);

  @override
  List<Object?> get props => [calculationType];
}
