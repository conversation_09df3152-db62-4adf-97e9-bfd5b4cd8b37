import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'settings_event.dart';
import 'settings_state.dart';
import '../models/settings_item.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  SettingsBloc() : super(const SettingsInitial()) {
    on<LoadSettings>(_onLoadSettings);
    on<ToggleWholesalePrice>(_onToggleWholesalePrice);
    on<TogglePartyWiseItemPrice>(_onTogglePartyWiseItemPrice);
    on<ToggleBarcodeScanner>(_onToggleBarcodeScanner);
    on<ToggleReminderForService>(_onToggleReminderForService);
    on<ToggleSerialNumberIMEI>(_onToggleSerialNumberIMEI);
    on<ToggleBatching>(_onToggleBatching);
    on<NavigateToAddRemoveColumns>(_onNavigateToAddRemoveColumns);
    on<NavigateToGodownWarehouse>(_onNavigateToGodownWarehouse);
    on<ChangeStockValueCalculation>(_onChangeStockValueCalculation);
  }

  Future<void> _onLoadSettings(
    LoadSettings event,
    Emitter<SettingsState> emit,
  ) async {
    emit(const SettingsLoading());

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Initialize default settings values
      const wholesalePriceEnabled = false;
      const partyWiseItemPriceEnabled = true;
      const barcodeScannerEnabled = false;
      const reminderForServiceEnabled = false;
      const serialNumberIMEIEnabled = false;
      const batchingEnabled = false;
      const stockValueCalculationType = 'Purchase Price';

      // Create settings sections
      final sections = _buildSettingsSections(
        wholesalePriceEnabled: wholesalePriceEnabled,
        partyWiseItemPriceEnabled: partyWiseItemPriceEnabled,
        barcodeScannerEnabled: barcodeScannerEnabled,
        reminderForServiceEnabled: reminderForServiceEnabled,
        serialNumberIMEIEnabled: serialNumberIMEIEnabled,
        batchingEnabled: batchingEnabled,
        stockValueCalculationType: stockValueCalculationType,
      );

      emit(SettingsLoaded(
        wholesalePriceEnabled: wholesalePriceEnabled,
        partyWiseItemPriceEnabled: partyWiseItemPriceEnabled,
        barcodeScannerEnabled: barcodeScannerEnabled,
        reminderForServiceEnabled: reminderForServiceEnabled,
        serialNumberIMEIEnabled: serialNumberIMEIEnabled,
        batchingEnabled: batchingEnabled,
        stockValueCalculationType: stockValueCalculationType,
        sections: sections,
      ));
    } catch (e) {
      emit(SettingsError('Failed to load settings: ${e.toString()}'));
    }
  }

  Future<void> _onToggleWholesalePrice(
    ToggleWholesalePrice event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: event.isEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        wholesalePriceEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onTogglePartyWiseItemPrice(
    TogglePartyWiseItemPrice event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: event.isEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        partyWiseItemPriceEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onToggleBarcodeScanner(
    ToggleBarcodeScanner event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: event.isEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        barcodeScannerEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onToggleReminderForService(
    ToggleReminderForService event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: event.isEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        reminderForServiceEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onToggleSerialNumberIMEI(
    ToggleSerialNumberIMEI event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: event.isEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        serialNumberIMEIEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onToggleBatching(
    ToggleBatching event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: event.isEnabled,
        stockValueCalculationType: currentState.stockValueCalculationType,
      );

      emit(currentState.copyWith(
        batchingEnabled: event.isEnabled,
        sections: updatedSections,
      ));
    }
  }

  Future<void> _onNavigateToAddRemoveColumns(
    NavigateToAddRemoveColumns event,
    Emitter<SettingsState> emit,
  ) async {
    // Handle navigation logic here
    // This could trigger navigation through a navigation service
    print('Navigating to Add/Remove Columns');
  }

  Future<void> _onNavigateToGodownWarehouse(
    NavigateToGodownWarehouse event,
    Emitter<SettingsState> emit,
  ) async {
    // Handle navigation logic here
    print('Navigating to Godown/Warehouse');
  }

  Future<void> _onChangeStockValueCalculation(
    ChangeStockValueCalculation event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSections = _buildSettingsSections(
        wholesalePriceEnabled: currentState.wholesalePriceEnabled,
        partyWiseItemPriceEnabled: currentState.partyWiseItemPriceEnabled,
        barcodeScannerEnabled: currentState.barcodeScannerEnabled,
        reminderForServiceEnabled: currentState.reminderForServiceEnabled,
        serialNumberIMEIEnabled: currentState.serialNumberIMEIEnabled,
        batchingEnabled: currentState.batchingEnabled,
        stockValueCalculationType: event.calculationType,
      );

      emit(currentState.copyWith(
        stockValueCalculationType: event.calculationType,
        sections: updatedSections,
      ));
    }
  }

  List<SettingsSection> _buildSettingsSections({
    required bool wholesalePriceEnabled,
    required bool partyWiseItemPriceEnabled,
    required bool barcodeScannerEnabled,
    required bool reminderForServiceEnabled,
    required bool serialNumberIMEIEnabled,
    required bool batchingEnabled,
    required String stockValueCalculationType,
  }) {
    return [
      SettingsSection(
        title: '',
        items: [
          SettingsItem(
            id: 'add_remove_columns',
            title: 'Add/Remove Columns',
            subtitle: 'Add extra fields in your Items & Invoices. Eg: MRP, Mfg. Date and other Custom Fields',
            icon: Icons.view_column,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.navigation,
            hasArrow: true,
            onTap: () => add(const NavigateToAddRemoveColumns()),
          ),
          SettingsItem(
            id: 'wholesale_price',
            title: 'Wholesale Price',
            subtitle: 'Enable wholesale price to sell items in bulk',
            icon: Icons.store,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: wholesalePriceEnabled,
            onToggle: (value) => add(ToggleWholesalePrice(value)),
          ),
          SettingsItem(
            id: 'party_wise_item_price',
            title: 'Party Wise Item Price',
            subtitle: 'Set custom Sales Prices for individual Parties',
            icon: Icons.diamond,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: partyWiseItemPriceEnabled,
            onToggle: (value) => add(TogglePartyWiseItemPrice(value)),
          ),
          SettingsItem(
            id: 'barcode_scanner',
            title: 'Barcode Scanner',
            subtitle: 'Enables you to scan and generate item barcodes',
            icon: Icons.qr_code_scanner,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: barcodeScannerEnabled,
            onToggle: (value) => add(ToggleBarcodeScanner(value)),
          ),
          SettingsItem(
            id: 'reminder_for_service',
            title: 'Reminder for service',
            subtitle: 'Reminder will be sent to you & your party 3 days before servicing date',
            icon: Icons.notifications,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: reminderForServiceEnabled,
            onToggle: (value) => add(ToggleReminderForService(value)),
          ),
        ],
      ),
      SettingsSection(
        title: 'Inventory Tracking By',
        items: [
          SettingsItem(
            id: 'serial_number_imei',
            title: 'Serial Number / IMEI',
            subtitle: 'Keep track of your inventory using unique codes like serial numbers and IMEI',
            icon: Icons.confirmation_number,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: serialNumberIMEIEnabled,
            onToggle: (value) => add(ToggleSerialNumberIMEI(value)),
          ),
          SettingsItem(
            id: 'batching',
            title: 'Batching',
            subtitle: 'Create items batch-wise and maintain separate prices and other details',
            icon: Icons.inventory_2,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.toggle,
            isEnabled: batchingEnabled,
            onToggle: (value) => add(ToggleBatching(value)),
          ),
          SettingsItem(
            id: 'godown_warehouse',
            title: 'Godown/Warehouse',
            subtitle: '',
            icon: Icons.warehouse,
            iconColor: const Color(0xFF5A67D8),
            type: SettingsItemType.navigation,
            hasArrow: true,
            onTap: () => add(const NavigateToGodownWarehouse()),
          ),
        ],
      ),
      SettingsSection(
        title: 'Other Options',
        items: [
          SettingsItem(
            id: 'stock_value_calculation',
            title: 'Stock value calculation using',
            subtitle: '',
            icon: null,
            type: SettingsItemType.selection,
            options: ['Sales Price', 'Purchase Price'],
            selectedOption: stockValueCalculationType,
            onTap: () => add(ChangeStockValueCalculation(
              stockValueCalculationType == 'Sales Price' ? 'Purchase Price' : 'Sales Price',
            )),
          ),
        ],
      ),
    ];
  }
}
