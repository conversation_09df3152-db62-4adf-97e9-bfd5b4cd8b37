import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_state.dart';

class StockSummaryCard extends StatelessWidget {
  const StockSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) =>
          previous.currentStock != current.currentStock ||
          previous.updatedStock != current.updatedStock ||
          previous.unit != current.unit ||
          previous.quantity != current.quantity ||
          previous.stockType != current.stockType,
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: [
              // Current Stock
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Current Stock',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                  AppText(
                    '${state.currentStock.toStringAsFixed(1)} ${state.unit}',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ],
              ),

              // Updated Stock (only show if quantity > 0)
              if (state.quantity > 0) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const AppText(
                      'Updated Stock',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                    AppText(
                      '${state.updatedStock.toStringAsFixed(1)} ${state.unit}',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: state.stockType == 'add'
                          ? Colors.green
                          : Colors.red,
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
