import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';

class SaveButton extends StatelessWidget {
  const SaveButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) =>
          previous.canSave != current.canSave ||
          previous.isLoading != current.isLoading ||
          previous.quantity != current.quantity,
      builder: (context, state) {
        final bool isEnabled = state.canSave && !state.isLoading;
        final Color buttonColor = state.quantity > 0
            ? const Color(0xFF5A67D8)
            : const Color(0xFFB8B8D1);

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: isEnabled ? () => _onSavePressed(context) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  disabledBackgroundColor: const Color(0xFFB8B8D1),
                  disabledForegroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: state.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const AppText(
                        'Save',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onSavePressed(BuildContext context) {
    HapticFeedback.mediumImpact();
    context.read<AdjustStockBloc>().add(const SaveStockAdjustment());
  }
}
