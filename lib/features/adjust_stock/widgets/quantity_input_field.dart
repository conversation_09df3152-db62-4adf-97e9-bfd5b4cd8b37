import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';

class SmartNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Allow empty string
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Only allow numbers and one decimal point
    final RegExp regex = RegExp(r'^\d*\.?\d*$');
    if (!regex.hasMatch(newValue.text)) {
      return oldValue;
    }

    // Prevent multiple decimal points
    if (newValue.text.split('.').length > 2) {
      return oldValue;
    }

    // Limit decimal places to 2
    if (newValue.text.contains('.')) {
      final parts = newValue.text.split('.');
      if (parts.length == 2 && parts[1].length > 2) {
        return oldValue;
      }
    }

    return newValue;
  }
}

class QuantityInputField extends StatefulWidget {
  const QuantityInputField({super.key});

  @override
  State<QuantityInputField> createState() => _QuantityInputFieldState();
}

class _QuantityInputFieldState extends State<QuantityInputField> {
  late TextEditingController _controller;
  bool _isInternalUpdate = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatQuantityForDisplay(double quantity) {
    // If the quantity is a whole number, display without decimals
    if (quantity == quantity.toInt()) {
      return quantity.toInt().toString();
    }
    // Otherwise, display with up to 2 decimal places, removing trailing zeros
    return quantity.toStringAsFixed(2).replaceAll(RegExp(r'\.?0+$'), '');
  }

  bool _shouldUpdateController(String currentText, double stateQuantity) {
    // Don't update if we're in the middle of an internal update
    if (_isInternalUpdate) return false;

    // Don't update if the field is empty and quantity is 0
    if (currentText.isEmpty && stateQuantity == 0) return false;

    // Parse current text to compare with state
    final currentValue = double.tryParse(currentText) ?? 0.0;

    // Only update if the values are actually different
    return currentValue != stateQuantity;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) =>
          previous.unit != current.unit ||
          previous.stockType != current.stockType ||
          _shouldUpdateController(_controller.text, current.quantity),
      builder: (context, state) {
        // Update controller text if quantity changed from outside (e.g., validation, reset)
        if (_shouldUpdateController(_controller.text, state.quantity)) {
          final newText = _formatQuantityForDisplay(state.quantity);
          final currentSelection = _controller.selection;

          _isInternalUpdate = true;
          _controller.text = newText;

          // Preserve cursor position if possible
          if (currentSelection.isValid &&
              currentSelection.start <= newText.length) {
            _controller.selection = currentSelection;
          }

          // Reset the flag after a brief delay
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _isInternalUpdate = false;
          });
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'Please add quantity',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [SmartNumberFormatter()],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Ex: 35',
                        hintStyle: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 16,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                      ),
                      onChanged: (value) {
                        if (!_isInternalUpdate) {
                          final quantity = double.tryParse(value) ?? 0.0;
                          context.read<AdjustStockBloc>().add(
                            UpdateQuantity(quantity),
                          );
                        }
                      },
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                      border: Border(
                        left: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppText(
                          state.unit,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[600],
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (state.stockType == 'reduce' &&
                state.quantity > state.currentStock)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: AppText(
                  'Quantity cannot exceed current stock (${state.currentStock} ${state.unit})',
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
          ],
        );
      },
    );
  }
}
