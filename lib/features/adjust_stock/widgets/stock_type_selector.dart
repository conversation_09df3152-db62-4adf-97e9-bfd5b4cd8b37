import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';

class StockTypeSelector extends StatelessWidget {
  const StockTypeSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) => previous.stockType != current.stockType,
      builder: (context, state) {
        return Row(
          children: [
            Expanded(
              child: _buildTypeButton(
                context: context,
                label: 'Add Stock',
                isSelected: state.stockType == 'add',
                icon: Icons.radio_button_checked,
                color: const Color(0xFF5A67D8),
                onTap: () => _onTypeSelected(context, 'add'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTypeButton(
                context: context,
                label: 'Reduce Stock',
                isSelected: state.stockType == 'reduce',
                icon: Icons.radio_button_unchecked,
                color: Colors.grey,
                onTap: () => _onTypeSelected(context, 'reduce'),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTypeButton({
    required BuildContext context,
    required String label,
    required bool isSelected,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final selectedColor = const Color(0xFF5A67D8);
    final unselectedColor = Colors.grey;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? selectedColor.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? selectedColor : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected ? selectedColor : unselectedColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            AppText(
              label,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isSelected ? selectedColor : Colors.grey[700],
            ),
          ],
        ),
      ),
    );
  }

  void _onTypeSelected(BuildContext context, String type) {
    HapticFeedback.lightImpact();
    context.read<AdjustStockBloc>().add(ChangeStockType(type));
  }
}
