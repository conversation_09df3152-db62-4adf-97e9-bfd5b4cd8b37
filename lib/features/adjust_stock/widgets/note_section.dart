import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';

class NoteSection extends StatefulWidget {
  const NoteSection({super.key});

  @override
  State<NoteSection> createState() => _NoteSectionState();
}

class _NoteSectionState extends State<NoteSection> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) =>
          previous.showNoteField != current.showNoteField ||
          previous.note != current.note,
      builder: (context, state) {
        // Update controller text if note changed from outside
        if (_controller.text != state.note) {
          _controller.text = state.note;
        }

        if (!state.showNoteField) {
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              context.read<AdjustStockBloc>().add(const ToggleNoteField());
            },
            child: Row(
              children: [
                Icon(Icons.add, color: const Color(0xFF5A67D8), size: 20),
                const SizedBox(width: 8),
                AppText(
                  'Add Note/ Description',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF5A67D8),
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: AppText(
                    'Add Note/ Description',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
                IconButton(
                  color: Colors.red,
                  visualDensity: VisualDensity.adaptivePlatformDensity,
                  style: ButtonStyle(
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    minimumSize: WidgetStatePropertyAll(Size.zero),
                    padding: WidgetStatePropertyAll(EdgeInsets.zero),
                  ),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    context.read<AdjustStockBloc>().add(
                      const ToggleNoteField(),
                    );
                  },
                  icon: Icon(Icons.remove_rounded),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.03),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  TextField(
                    controller: _controller,
                    maxLines: 3,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Colors.black87,
                    ),
                    decoration: InputDecoration(
                      hintText: 'Ex: Good expired',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.fromLTRB(
                        16,
                        10,
                        state.note.isNotEmpty
                            ? 48
                            : 16, // Add space for clear button
                        10,
                      ),
                    ),
                    onChanged: (value) {
                      context.read<AdjustStockBloc>().add(UpdateNote(value));
                    },
                  ),
                  // Clear button - only show when there's text
                  if (state.note.isNotEmpty)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _controller.clear();
                          context.read<AdjustStockBloc>().add(
                            const UpdateNote(''),
                          );
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF5A67D8,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.clear,
                            color: Color(0xFF5A67D8),
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
