import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';
import '../widgets/stock_type_selector.dart';
import '../widgets/date_picker_field.dart';
import '../widgets/quantity_input_field.dart';
import '../widgets/note_section.dart';
import '../widgets/stock_summary_card.dart';
import '../widgets/save_button.dart';

class AdjustStockScreen extends StatelessWidget {
  final Map<String, dynamic> item;

  const AdjustStockScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AdjustStockBloc()..add(InitializeAdjustStock(item)),
      child: const _AdjustStockView(),
    );
  }
}

class _AdjustStockView extends StatelessWidget {
  const _AdjustStockView();

  @override
  Widget build(BuildContext context) {
    return BlocListener<AdjustStockBloc, AdjustStockState>(
      listener: (context, state) {
        if (state.isSuccess) {
          HapticFeedback.mediumImpact();
          Navigator.of(context).pop(true); // Return true to indicate success
        }

        if (state.errorMessage != null) {
          HapticFeedback.heavyImpact();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: GestureDetector(
        onTap: () {
          // Unfocus any focused text fields when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF5F5F5),
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: AnimationLimiter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 300),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 30.0,
                            child: FadeInAnimation(child: widget),
                          ),
                          children: [
                            const StockTypeSelector(),
                            const SizedBox(height: 24),
                            const DatePickerField(),
                            const SizedBox(height: 24),
                            const QuantityInputField(),
                            const SizedBox(height: 24),
                            const NoteSection(),
                            const SizedBox(height: 24),
                            const StockSummaryCard(),
                            const SizedBox(height: 100),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: const SaveButton(),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            style: ButtonStyle(
              padding: WidgetStatePropertyAll(EdgeInsets.zero),
              minimumSize: WidgetStatePropertyAll(Size.zero),
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).pop();
            },
            icon: Icon(
              Platform.isIOS
                  ? Icons.arrow_back_ios_new_rounded
                  : Icons.arrow_back,
              color: Colors.black87,
              size: 24,
            ),
          ),
          const SizedBox(width: 8),
          const AppText(
            'Adjust Stock',
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ],
      ),
    );
  }
}
