import 'package:flutter_bloc/flutter_bloc.dart';
import 'adjust_stock_event.dart';
import 'adjust_stock_state.dart';

class AdjustStockBloc extends Bloc<AdjustStockEvent, AdjustStockState> {
  AdjustStockBloc() : super(AdjustStockState.initial()) {
    on<InitializeAdjustStock>(_onInitializeAdjustStock);
    on<ChangeStockType>(_onChangeStockType);
    on<UpdateQuantity>(_onUpdateQuantity);
    on<UpdateDate>(_onUpdateDate);
    on<UpdateNote>(_onUpdateNote);
    on<ToggleNoteField>(_onToggleNoteField);
    on<SaveStockAdjustment>(_onSaveStockAdjustment);
  }

  void _onInitializeAdjustStock(
    InitializeAdjustStock event,
    Emitter<AdjustStockState> emit,
  ) {
    // Check if initialStockType is provided in the item
    final String initialStockType = event.item['initialStockType'] ?? 'add';

    emit(
      state.copyWith(
        item: event.item,
        stockType: initialStockType,
        quantity: 0.0,
        note: '',
        showNoteField: false,
        isLoading: false,
        errorMessage: null,
        isSuccess: false,
      ),
    );
  }

  void _onChangeStockType(
    ChangeStockType event,
    Emitter<AdjustStockState> emit,
  ) {
    emit(state.copyWith(stockType: event.stockType));
  }

  void _onUpdateQuantity(UpdateQuantity event, Emitter<AdjustStockState> emit) {
    // Validate quantity based on stock type
    double validatedQuantity = event.quantity;

    if (event.quantity < 0) {
      validatedQuantity = 0;
    }

    // For reduce stock, ensure quantity doesn't exceed current stock
    if (state.stockType == 'reduce' && validatedQuantity > state.currentStock) {
      validatedQuantity = state.currentStock;
    }

    emit(state.copyWith(quantity: validatedQuantity));
  }

  void _onUpdateDate(UpdateDate event, Emitter<AdjustStockState> emit) {
    emit(state.copyWith(selectedDate: event.date));
  }

  void _onUpdateNote(UpdateNote event, Emitter<AdjustStockState> emit) {
    emit(state.copyWith(note: event.note));
  }

  void _onToggleNoteField(
    ToggleNoteField event,
    Emitter<AdjustStockState> emit,
  ) {
    emit(state.copyWith(showNoteField: !state.showNoteField));
  }

  void _onSaveStockAdjustment(
    SaveStockAdjustment event,
    Emitter<AdjustStockState> emit,
  ) async {
    if (!state.canSave) return;

    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // TODO: Implement actual API call to save stock adjustment
      // This would typically involve:
      // 1. Calling a repository method to save the adjustment
      // 2. Updating the item's stock in the database
      // 3. Creating an audit trail entry

      emit(state.copyWith(isLoading: false, isSuccess: true));
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to save stock adjustment: ${error.toString()}',
        ),
      );
    }
  }
}
