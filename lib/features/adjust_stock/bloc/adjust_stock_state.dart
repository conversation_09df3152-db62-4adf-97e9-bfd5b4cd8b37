import 'package:equatable/equatable.dart';

class AdjustStockState extends Equatable {
  final Map<String, dynamic>? item;
  final String stockType; // 'add' or 'reduce'
  final double quantity;
  final DateTime selectedDate;
  final String note;
  final bool showNoteField;
  final bool isLoading;
  final String? errorMessage;
  final bool isSuccess;

  const AdjustStockState({
    this.item,
    this.stockType = 'add',
    this.quantity = 0.0,
    required this.selectedDate,
    this.note = '',
    this.showNoteField = false,
    this.isLoading = false,
    this.errorMessage,
    this.isSuccess = false,
  });

  factory AdjustStockState.initial() {
    return AdjustStockState(selectedDate: DateTime.now());
  }

  double get currentStock => item?['stock']?.toDouble() ?? 0.0;

  String get unit => item?['unit'] ?? 'PCS';

  double get updatedStock {
    if (stockType == 'add') {
      return currentStock + quantity;
    } else {
      return currentStock - quantity;
    }
  }

  bool get canSave {
    return quantity > 0 &&
        (stockType == 'add' || quantity <= currentStock) &&
        !isLoading;
  }

  AdjustStockState copyWith({
    Map<String, dynamic>? item,
    String? stockType,
    double? quantity,
    DateTime? selectedDate,
    String? note,
    bool? showNoteField,
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
  }) {
    return AdjustStockState(
      item: item ?? this.item,
      stockType: stockType ?? this.stockType,
      quantity: quantity ?? this.quantity,
      selectedDate: selectedDate ?? this.selectedDate,
      note: note ?? this.note,
      showNoteField: showNoteField ?? this.showNoteField,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }

  @override
  List<Object?> get props => [
    item,
    stockType,
    quantity,
    selectedDate,
    note,
    showNoteField,
    isLoading,
    errorMessage,
    isSuccess,
  ];
}
