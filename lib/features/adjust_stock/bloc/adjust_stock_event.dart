import 'package:equatable/equatable.dart';

abstract class AdjustStockEvent extends Equatable {
  const AdjustStockEvent();

  @override
  List<Object?> get props => [];
}

class InitializeAdjustStock extends AdjustStockEvent {
  final Map<String, dynamic> item;

  const InitializeAdjustStock(this.item);

  @override
  List<Object?> get props => [item];
}

class ChangeStockType extends AdjustStockEvent {
  final String stockType; // 'add' or 'reduce'

  const ChangeStockType(this.stockType);

  @override
  List<Object?> get props => [stockType];
}

class UpdateQuantity extends AdjustStockEvent {
  final double quantity;

  const UpdateQuantity(this.quantity);

  @override
  List<Object?> get props => [quantity];
}

class UpdateDate extends AdjustStockEvent {
  final DateTime date;

  const UpdateDate(this.date);

  @override
  List<Object?> get props => [date];
}

class UpdateNote extends AdjustStockEvent {
  final String note;

  const UpdateNote(this.note);

  @override
  List<Object?> get props => [note];
}

class ToggleNoteField extends AdjustStockEvent {
  const ToggleNoteField();
}

class SaveStockAdjustment extends AdjustStockEvent {
  const SaveStockAdjustment();
}
