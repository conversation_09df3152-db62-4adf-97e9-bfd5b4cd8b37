import 'package:equatable/equatable.dart';

abstract class ReminderSettingsState extends Equatable {
  const ReminderSettingsState();

  @override
  List<Object?> get props => [];
}

class ReminderSettingsInitial extends ReminderSettingsState {
  const ReminderSettingsInitial();
}

class ReminderSettingsLoading extends ReminderSettingsState {
  const ReminderSettingsLoading();
}

class ReminderSettingsLoaded extends ReminderSettingsState {
  final bool smsToPartyEnabled;
  final bool partyPaymentReminderEnabled;
  final bool yourPaymentReminderEnabled;
  final bool dailyOutstandingPaymentsEnabled;
  final bool dailySalesSummaryEnabled;
  final bool lowStockAlertEnabled;
  final bool serviceReminderEnabled;
  final bool birthdayRemindersEnabled;
  final bool whatsAppAlertEnabled;

  const ReminderSettingsLoaded({
    required this.smsToPartyEnabled,
    required this.partyPaymentReminderEnabled,
    required this.yourPaymentReminderEnabled,
    required this.dailyOutstandingPaymentsEnabled,
    required this.dailySalesSummaryEnabled,
    required this.lowStockAlertEnabled,
    required this.serviceReminderEnabled,
    required this.birthdayRemindersEnabled,
    required this.whatsAppAlertEnabled,
  });

  ReminderSettingsLoaded copyWith({
    bool? smsToPartyEnabled,
    bool? partyPaymentReminderEnabled,
    bool? yourPaymentReminderEnabled,
    bool? dailyOutstandingPaymentsEnabled,
    bool? dailySalesSummaryEnabled,
    bool? lowStockAlertEnabled,
    bool? serviceReminderEnabled,
    bool? birthdayRemindersEnabled,
    bool? whatsAppAlertEnabled,
  }) {
    return ReminderSettingsLoaded(
      smsToPartyEnabled: smsToPartyEnabled ?? this.smsToPartyEnabled,
      partyPaymentReminderEnabled: partyPaymentReminderEnabled ?? this.partyPaymentReminderEnabled,
      yourPaymentReminderEnabled: yourPaymentReminderEnabled ?? this.yourPaymentReminderEnabled,
      dailyOutstandingPaymentsEnabled: dailyOutstandingPaymentsEnabled ?? this.dailyOutstandingPaymentsEnabled,
      dailySalesSummaryEnabled: dailySalesSummaryEnabled ?? this.dailySalesSummaryEnabled,
      lowStockAlertEnabled: lowStockAlertEnabled ?? this.lowStockAlertEnabled,
      serviceReminderEnabled: serviceReminderEnabled ?? this.serviceReminderEnabled,
      birthdayRemindersEnabled: birthdayRemindersEnabled ?? this.birthdayRemindersEnabled,
      whatsAppAlertEnabled: whatsAppAlertEnabled ?? this.whatsAppAlertEnabled,
    );
  }

  @override
  List<Object?> get props => [
        smsToPartyEnabled,
        partyPaymentReminderEnabled,
        yourPaymentReminderEnabled,
        dailyOutstandingPaymentsEnabled,
        dailySalesSummaryEnabled,
        lowStockAlertEnabled,
        serviceReminderEnabled,
        birthdayRemindersEnabled,
        whatsAppAlertEnabled,
      ];
}

class ReminderSettingsError extends ReminderSettingsState {
  final String message;

  const ReminderSettingsError(this.message);

  @override
  List<Object?> get props => [message];
}

class ReminderSettingsSaving extends ReminderSettingsState {
  const ReminderSettingsSaving();
}

class ReminderSettingsSaved extends ReminderSettingsState {
  const ReminderSettingsSaved();
}
