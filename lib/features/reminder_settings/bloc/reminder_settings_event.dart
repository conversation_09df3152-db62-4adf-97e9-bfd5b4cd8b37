import 'package:equatable/equatable.dart';

abstract class ReminderSettingsEvent extends Equatable {
  const ReminderSettingsEvent();

  @override
  List<Object?> get props => [];
}

class LoadReminderSettings extends ReminderSettingsEvent {
  const LoadReminderSettings();
}

class ToggleSMSToParty extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleSMSToParty(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class TogglePartyPaymentReminder extends ReminderSettingsEvent {
  final bool isEnabled;

  const TogglePartyPaymentReminder(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleYourPaymentReminder extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleYourPaymentReminder(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleDailyOutstandingPayments extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleDailyOutstandingPayments(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleDailySalesSummary extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleDailySalesSummary(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleLowStockAlert extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleLowStockAlert(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleServiceReminder extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleServiceReminder(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleBirthdayReminders extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleBirthdayReminders(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class ToggleWhatsAppAlert extends ReminderSettingsEvent {
  final bool isEnabled;

  const ToggleWhatsAppAlert(this.isEnabled);

  @override
  List<Object?> get props => [isEnabled];
}

class SaveReminderSettings extends ReminderSettingsEvent {
  const SaveReminderSettings();
}
