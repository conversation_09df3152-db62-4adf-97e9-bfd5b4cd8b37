import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'reminder_settings_event.dart';
import 'reminder_settings_state.dart';

class ReminderSettingsBloc
    extends Bloc<ReminderSettingsEvent, ReminderSettingsState> {
  ReminderSettingsBloc() : super(const ReminderSettingsInitial()) {
    on<LoadReminderSettings>(_onLoadReminderSettings);
    on<ToggleSMSToParty>(_onToggleSMSToParty);
    on<TogglePartyPaymentReminder>(_onTogglePartyPaymentReminder);
    on<ToggleYourPaymentReminder>(_onToggleYourPaymentReminder);
    on<ToggleDailyOutstandingPayments>(_onToggleDailyOutstandingPayments);
    on<ToggleDailySalesSummary>(_onToggleDailySalesSummary);
    on<ToggleLowStockAlert>(_onToggleLowStockAlert);
    on<ToggleServiceReminder>(_onToggleServiceReminder);
    on<ToggleBirthdayReminders>(_onToggleBirthdayReminders);
    on<ToggleWhatsAppAlert>(_onToggleWhatsAppAlert);
    on<SaveReminderSettings>(_onSaveReminderSettings);
  }

  Future<void> _onLoadReminderSettings(
    LoadReminderSettings event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    emit(const ReminderSettingsLoading());

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Load reminder settings (in a real app, this would come from a repository/service)
      // Based on the reference image, most toggles appear to be enabled by default
      emit(
        const ReminderSettingsLoaded(
          smsToPartyEnabled: true,
          partyPaymentReminderEnabled: true,
          yourPaymentReminderEnabled: true,
          dailyOutstandingPaymentsEnabled: true,
          dailySalesSummaryEnabled: true,
          lowStockAlertEnabled: true,
          serviceReminderEnabled: false, // This appears disabled in the image
          birthdayRemindersEnabled: false, // This appears disabled in the image
          whatsAppAlertEnabled: false, // This appears disabled in the image
        ),
      );
    } catch (e) {
      emit(
        ReminderSettingsError(
          'Failed to load reminder settings: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onToggleSMSToParty(
    ToggleSMSToParty event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(smsToPartyEnabled: event.isEnabled));
    }
  }

  Future<void> _onTogglePartyPaymentReminder(
    TogglePartyPaymentReminder event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(partyPaymentReminderEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleYourPaymentReminder(
    ToggleYourPaymentReminder event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(yourPaymentReminderEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleDailyOutstandingPayments(
    ToggleDailyOutstandingPayments event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(dailyOutstandingPaymentsEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleDailySalesSummary(
    ToggleDailySalesSummary event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(dailySalesSummaryEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleLowStockAlert(
    ToggleLowStockAlert event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(lowStockAlertEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleServiceReminder(
    ToggleServiceReminder event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(serviceReminderEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleBirthdayReminders(
    ToggleBirthdayReminders event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(birthdayRemindersEnabled: event.isEnabled));
    }
  }

  Future<void> _onToggleWhatsAppAlert(
    ToggleWhatsAppAlert event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      HapticFeedback.lightImpact();
      final currentState = state as ReminderSettingsLoaded;
      emit(currentState.copyWith(whatsAppAlertEnabled: event.isEnabled));
    }
  }

  Future<void> _onSaveReminderSettings(
    SaveReminderSettings event,
    Emitter<ReminderSettingsState> emit,
  ) async {
    if (state is ReminderSettingsLoaded) {
      emit(const ReminderSettingsSaving());

      try {
        // Simulate saving delay
        await Future.delayed(const Duration(milliseconds: 500));

        // Save settings (in a real app, this would save to a repository/service)
        emit(const ReminderSettingsSaved());

        // Return to loaded state
        await Future.delayed(const Duration(milliseconds: 100));
        add(const LoadReminderSettings());
      } catch (e) {
        emit(
          ReminderSettingsError(
            'Failed to save reminder settings: ${e.toString()}',
          ),
        );
      }
    }
  }
}
