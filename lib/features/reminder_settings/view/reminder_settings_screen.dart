import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/reminder_settings_bloc.dart';
import '../bloc/reminder_settings_event.dart';
import '../bloc/reminder_settings_state.dart';

class ReminderSettingsScreen extends StatefulWidget {
  const ReminderSettingsScreen({super.key});

  @override
  State<ReminderSettingsScreen> createState() => _ReminderSettingsScreenState();
}

class _ReminderSettingsScreenState extends State<ReminderSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          ReminderSettingsBloc()..add(const LoadReminderSettings()),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        body: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(context),

              // Content
              Expanded(
                child: BlocBuilder<ReminderSettingsBloc, ReminderSettingsState>(
                  builder: (context, state) {
                    if (state is ReminderSettingsLoading) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF5A67D8),
                        ),
                      );
                    } else if (state is ReminderSettingsLoaded) {
                      return _buildReminderSettingsContent(context, state);
                    } else if (state is ReminderSettingsError) {
                      return Center(
                        child: AppText(
                          state.message,
                          fontSize: 16,
                          color: Colors.red,
                          textAlign: TextAlign.center,
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 350),
      child: SlideAnimation(
        horizontalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
              ),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Color(0xFF5A67D8),
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                const AppText(
                  'Reminder Settings',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReminderSettingsContent(
    BuildContext context,
    ReminderSettingsLoaded state,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            delay: const Duration(milliseconds: 100),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
            ),
            children: [
              // Party Reminders Section
              _buildSectionHeader('Party Reminders'),
              const SizedBox(height: 12),
              _buildReminderItem(
                icon: Icons.sms,
                iconColor: const Color(0xFF5A67D8),
                title: 'Send SMS to party on creating transactions',
                isEnabled: state.smsToPartyEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleSMSToParty(value),
                ),
              ),
              const SizedBox(height: 8),
              _buildReminderItem(
                icon: Icons.payment,
                iconColor: const Color(0xFF5A67D8),
                title: 'Payment Reminder on due date',
                isEnabled: state.partyPaymentReminderEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  TogglePartyPaymentReminder(value),
                ),
              ),
              const SizedBox(height: 24),

              // Your Reminders Section
              _buildSectionHeader('Your Reminders'),
              const SizedBox(height: 12),
              _buildReminderItem(
                icon: Icons.notifications,
                iconColor: const Color(0xFF5A67D8),
                title: 'Payment Reminder on due date',
                isEnabled: state.yourPaymentReminderEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleYourPaymentReminder(value),
                ),
              ),
              const SizedBox(height: 8),
              _buildReminderItem(
                icon: Icons.currency_rupee,
                iconColor: const Color(0xFF5A67D8),
                title: 'Daily Outstanding Payments',
                isEnabled: state.dailyOutstandingPaymentsEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleDailyOutstandingPayments(value),
                ),
              ),
              const SizedBox(height: 8),
              _buildReminderItem(
                icon: Icons.receipt_long,
                iconColor: const Color(0xFF5A67D8),
                title: 'Daily Sales Summary',
                isEnabled: state.dailySalesSummaryEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleDailySalesSummary(value),
                ),
              ),
              const SizedBox(height: 8),
              _buildReminderItem(
                icon: Icons.inventory_2,
                iconColor: const Color(0xFF5A67D8),
                title: 'Low stock alert',
                isEnabled: state.lowStockAlertEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleLowStockAlert(value),
                ),
              ),
              const SizedBox(height: 24),

              // WhatsApp Reminder Section
              _buildSectionHeader('Whatsapp Reminder'),
              const SizedBox(height: 12),
              _buildServiceReminderItem(state),
              const SizedBox(height: 8),
              _buildReminderItem(
                icon: Icons.cake,
                iconColor: const Color(0xFF5A67D8),
                title: 'Birthday reminders',
                isEnabled: state.birthdayRemindersEnabled,
                onToggle: (value) => context.read<ReminderSettingsBloc>().add(
                  ToggleBirthdayReminders(value),
                ),
              ),
              const SizedBox(height: 8),
              _buildWhatsAppAlertItem(state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return AppText(
      title,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: Colors.grey[600],
    );
  }

  Widget _buildReminderItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required bool isEnabled,
    required Function(bool) onToggle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: AppText(
              title,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 16),
          Switch.adaptive(
            value: isEnabled,
            onChanged: onToggle,
            activeColor: const Color(0xFF5A67D8),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceReminderItem(ReminderSettingsLoaded state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF5A67D8).withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(Icons.build, color: Color(0xFF5A67D8), size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AppText(
                  'Reminder for service on due date',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                const SizedBox(height: 4),
                AppText(
                  'Reminder will be sent to you & your party 3 days before servicing date',
                  fontSize: 12,
                  color: Colors.grey[600],
                  maxLines: 2,
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Switch.adaptive(
            value: state.serviceReminderEnabled,
            onChanged: (value) => context.read<ReminderSettingsBloc>().add(
              ToggleServiceReminder(value),
            ),
            activeColor: const Color(0xFF5A67D8),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildWhatsAppAlertItem(ReminderSettingsLoaded state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(Icons.message, color: Colors.green, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AppText(
                  'WhatsApp Alert',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                const SizedBox(height: 4),
                AppText(
                  'Get reminders for collecting payments',
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Switch.adaptive(
            value: state.whatsAppAlertEnabled,
            onChanged: (value) => context.read<ReminderSettingsBloc>().add(
              ToggleWhatsAppAlert(value),
            ),
            activeColor: const Color(0xFF5A67D8),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }
}
