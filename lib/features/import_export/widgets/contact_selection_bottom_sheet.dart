import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:shimmer/shimmer.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/services/contact_permission_service.dart';
import 'package:bill_book/core/services/contact_service.dart';
import '../models/contact_item.dart';
import '../bloc/import_export_bloc.dart';
import '../bloc/import_export_event.dart';

class ContactSelectionBottomSheet extends StatefulWidget {
  final String contactType; // 'Customer' or 'Supplier'
  final List<String> initialSelectedContacts;

  const ContactSelectionBottomSheet({
    super.key,
    required this.contactType,
    required this.initialSelectedContacts,
  });

  @override
  State<ContactSelectionBottomSheet> createState() =>
      _ContactSelectionBottomSheetState();
}

class _ContactSelectionBottomSheetState
    extends State<ContactSelectionBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<ContactItem> _allContacts = [];
  List<ContactItem> _filteredContacts = [];
  Set<String> _selectedContactIds = {};
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _selectedContactIds = widget.initialSelectedContacts.toSet();
    _loadContacts();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Check if we need to refresh contacts when the bottom sheet is reopened
    _checkForNewContacts();
  }

  Future<void> _checkForNewContacts() async {
    // Only check if we already have some contacts loaded
    if (_allContacts.isNotEmpty && !_isLoading) {
      try {
        // Check if new contacts might be available
        final contacts = await ContactService.getContacts(forceRefresh: false);

        // If we got more contacts than before, refresh the list
        if (contacts.length > _allContacts.length) {
          await _loadContacts(forceRefresh: true);
        }
      } catch (e) {
        // Ignore errors during background check
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadContacts({
    bool forceRefresh = false,
    bool skipPermissionCheck = false,
  }) async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Check and request permission (skip if we just granted full access)
      if (!skipPermissionCheck) {
        final hasPermission =
            await ContactPermissionService.handleContactsPermission(context);
        if (!hasPermission) {
          setState(() {
            _isLoading = false;
            _errorMessage =
                'Contacts permission is required to import contacts.';
          });
          return;
        }
      }

      // Load contacts with support for iOS limited access
      final contacts = await ContactService.getContacts(
        forceRefresh: forceRefresh,
        allowLimitedAccess: true,
      );
      print('Loaded ${contacts.length} contacts');

      setState(() {
        _allContacts = contacts;
        _filteredContacts = contacts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _requestMoreContacts() async {
    print('Requesting more contacts...');
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Store current contact count
      final currentCount = _allContacts.length;

      // Request full contact access instead of limited access
      final gotFullAccess =
          await ContactPermissionService.requestFullContactAccess(context);
      print('Got full access: $gotFullAccess');

      if (gotFullAccess) {
        // Wait a moment for the permission change to fully propagate
        await Future.delayed(const Duration(milliseconds: 300));

        // Reload contacts with force refresh to get all contacts
        await _loadContacts(forceRefresh: true, skipPermissionCheck: true);

        // Check if we actually got new contacts
        if (_allContacts.length > currentCount) {
          print('New contacts added: ${_allContacts.length - currentCount}');
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Full access granted! Added ${_allContacts.length - currentCount} new contacts',
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        } else {
          print('Full access granted but no new contacts');
          // Show info message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Full access granted! All contacts are now available',
                ),
                backgroundColor: Colors.blue,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }

        setState(() {
          _isLoading = false;
        });
      } else {
        print('User denied full access');
        // User denied full access
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Full access was not granted'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    setState(() {
      _filteredContacts = ContactService.searchContacts(_allContacts, query);
    });
  }

  void _toggleContactSelection(String contactId) {
    setState(() {
      if (_selectedContactIds.contains(contactId)) {
        _selectedContactIds.remove(contactId);
      } else {
        _selectedContactIds.add(contactId);
      }
    });
    HapticFeedback.lightImpact();
  }

  void _clearAllSelections() {
    setState(() {
      _selectedContactIds.clear();
    });
    HapticFeedback.lightImpact();
  }

  void _onDone() {
    HapticFeedback.lightImpact();

    // Update the BLoC with selected contacts
    if (widget.contactType == 'Customer') {
      context.read<ImportExportBloc>().add(
        SelectCustomerContacts(_selectedContactIds.toList()),
      );
    } else {
      context.read<ImportExportBloc>().add(
        SelectSupplierContacts(_selectedContactIds.toList()),
      );
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          Expanded(child: _buildContactList()),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: AppText(
              'Select ${widget.contactType}',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).pop();
            },
            icon: const Icon(Icons.close, color: Colors.black54, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.search, color: Colors.grey, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search by Contact Name',
                hintStyle: TextStyle(color: Colors.grey, fontSize: 14),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactList() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_filteredContacts.isEmpty) {
      return _buildEmptyState();
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _filteredContacts.length,
        itemBuilder: (context, index) {
          final contact = _filteredContacts[index];
          final isSelected = _selectedContactIds.contains(contact.id);

          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 200),
            child: SlideAnimation(
              verticalOffset: 20.0,
              child: FadeInAnimation(
                child: _buildContactItem(contact, isSelected),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContactItem(ContactItem contact, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _toggleContactSelection(contact.id),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[200]!,
              ),
            ),
            child: Row(
              children: [
                // Contact Avatar
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF5A67D8),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: AppText(
                      ContactService.getContactInitials(contact.name),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Contact Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        contact.name,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (contact.phoneNumber != null) ...[
                        const SizedBox(height: 2),
                        AppText(
                          ContactService.formatPhoneNumber(contact.phoneNumber),
                          fontSize: 12,
                          color: Colors.grey[600],
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),

                // Checkbox
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF5A67D8)
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF5A67D8)
                          : Colors.grey[400]!,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 14)
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 8,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 14,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        height: 12,
                        width: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 32,
              ),
            ),
            const SizedBox(height: 16),
            const AppText(
              'Failed to Load Contacts',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            AppText(
              _errorMessage ?? 'An error occurred while loading contacts.',
              fontSize: 14,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadContacts,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5A67D8),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const AppText(
                'Retry',
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.contacts_outlined,
                color: Colors.grey,
                size: 32,
              ),
            ),
            const SizedBox(height: 16),
            const AppText(
              'No Contacts Found',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            AppText(
              _searchController.text.isNotEmpty
                  ? 'No contacts match your search criteria.'
                  : 'No contacts available on this device.',
              fontSize: 14,
              color: Colors.grey[600],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Request Full Access button (always show if not loading and no error)
            if (!_isLoading && _errorMessage == null) ...[
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _requestMoreContacts,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add, size: 16),
                  label: AppText(
                    _isLoading ? 'Loading...' : 'Request Full Access',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF5A67D8),
                    side: const BorderSide(color: Color(0xFF5A67D8)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // Add helpful text for users
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AppText(
                  _allContacts.isEmpty
                      ? 'Grant full access to see all your contacts and select multiple at once'
                      : 'Grant full access to see additional contacts and select multiple at once',
                  fontSize: 12,
                  color: Colors.grey,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Main action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _selectedContactIds.isNotEmpty
                        ? _clearAllSelections
                        : null,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                      side: BorderSide(color: Colors.grey[300]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const AppText(
                      'Clear All',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _onDone,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5A67D8),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: AppText(
                      _selectedContactIds.isNotEmpty
                          ? 'Select Contacts (${_selectedContactIds.length})'
                          : 'Select Contacts',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
