import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/widgets/app_text.dart';

Widget buildHeader(BuildContext context) {
  return AnimationConfiguration.staggeredList(
    position: 0,
    duration: const Duration(milliseconds: 350),
    child: SlideAnimation(
      verticalOffset: -20.0,
      curve: Curves.easeOutCubic,
      child: FadeInAnimation(
        curve: Curves.easeOutCubic,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          color: Colors.white,
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  Navigator.pop(context);
                },
                style: ButtonStyle(
                  padding: WidgetStatePropertyAll(EdgeInsets.zero),
                  minimumSize: WidgetStatePropertyAll(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                icon: Icon(
                  Platform.isIOS
                      ? Icons.arrow_back_ios_new_rounded
                      : Icons.arrow_back,
                  color: Colors.black87,
                  size: 24,
                ),
              ),
              const Expanded(
                child: AppText(
                  'Add Parties from Contacts',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
