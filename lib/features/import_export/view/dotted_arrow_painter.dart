import 'package:flutter/material.dart';

class DottedArrowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF5A67D8)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw dotted curved line
    final path = Path();
    path.moveTo(0, size.height / 2);
    path.quadraticBezierTo(size.width / 2, 0, size.width - 10, size.height / 2);

    // Create dotted effect
    final dashWidth = 3.0;
    final dashSpace = 3.0;
    double distance = 0.0;

    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      while (distance < pathMetric.length) {
        final extractPath = pathMetric.extractPath(
          distance,
          distance + dashWidth,
        );
        canvas.drawPath(extractPath, paint);
        distance += dashWidth + dashSpace;
      }
    }

    // Draw arrow head
    final arrowPaint = Paint()
      ..color = const Color(0xFF5A67D8)
      ..style = PaintingStyle.fill;

    final arrowPath = Path();
    arrowPath.moveTo(size.width - 10, size.height / 2 - 5);
    arrowPath.lineTo(size.width, size.height / 2);
    arrowPath.lineTo(size.width - 10, size.height / 2 + 5);
    arrowPath.close();

    canvas.drawPath(arrowPath, arrowPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
