import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/import_export_bloc.dart';
import '../bloc/import_export_event.dart';
import '../bloc/import_export_state.dart';
import '../widgets/contact_selection_bottom_sheet.dart';
import '../widgets/import_header.dart';
import 'dotted_arrow_painter.dart';

class ImportExportScreen extends StatelessWidget {
  const ImportExportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => ImportExportBloc()..add(const LoadImportExport()),
      child: const ImportExportView(),
    );
  }
}

class ImportExportView extends StatelessWidget {
  const ImportExportView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            buildHeader(context),
            Expanded(
              child: BlocBuilder<ImportExportBloc, ImportExportState>(
                builder: (context, state) {
                  if (state is ImportExportLoading) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (state is ImportExportError) {
                    return Center(
                      child: AppText(
                        'Error: ${state.message}',
                        color: Colors.red,
                        textAlign: TextAlign.center,
                      ),
                    );
                  } else if (state is PartiesCreated) {
                    return _buildSuccessView(context, state.createdCount);
                  } else if (state is ImportExportLoaded) {
                    return _buildMainContent(context, state);
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, ImportExportLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildIllustrationCard(),
              const SizedBox(height: 24),
              _buildDescriptionText(),
              const SizedBox(height: 32),
              _buildContactTypeCard(
                context,
                'Customer',
                state.selectedCustomerContacts.length,
                () => _navigateToContactSelection(context, 'Customer'),
              ),
              const SizedBox(height: 16),
              _buildContactTypeCard(
                context,
                'Supplier',
                state.selectedSupplierContacts.length,
                () => _navigateToContactSelection(context, 'Supplier'),
              ),
              const SizedBox(height: 32),
              _buildCreateButton(context, state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIllustrationCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Bulk Add Parties Button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: const Color(0xFF5A67D8)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.group_add, color: Color(0xFF5A67D8), size: 16),
                const SizedBox(width: 8),
                const AppText(
                  'Bulk Add Parties',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF5A67D8),
                ),
                const SizedBox(width: 4),
                Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFD700),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.star, color: Colors.white, size: 12),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Illustration
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Phonebook
              Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.phone,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const AppText(
                    'Phonebook',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ],
              ),

              // Arrow and myBillBook logo
              Column(
                children: [
                  // Dotted arrow
                  CustomPaint(
                    size: const Size(80, 40),
                    painter: DottedArrowPainter(),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const AppText(
                      'myBillBook',
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),

              // Parties
              Column(
                children: [
                  Column(
                    children: [
                      _buildPartyAvatar('Party 1', Colors.green),
                      const SizedBox(height: 4),
                      _buildPartyAvatar('Party 2', Colors.purple),
                      const SizedBox(height: 4),
                      _buildPartyAvatar('Party 3', Colors.blue),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPartyAvatar(String label, Color color) {
    return Container(
      width: 60,
      height: 20,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16,
            height: 16,
            margin: const EdgeInsets.only(left: 2),
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: AppText(
              label,
              fontSize: 8,
              color: color,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionText() {
    return Column(
      children: [
        const AppText(
          'Select contacts & create multiple Parties!',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 5),
        AppText(
          'For quicker and easier experience of creating sales invoices',
          fontSize: 12,
          color: Colors.grey[700],
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildContactTypeCard(
    BuildContext context,
    String type,
    int selectedCount,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    type,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  const SizedBox(height: 4),
                  AppText(
                    '$selectedCount Contacts Selected',
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF5A67D8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const AppText(
                    'Select',
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 4),
                  const Icon(
                    Icons.arrow_forward,
                    color: Colors.white,
                    size: 14,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton(BuildContext context, ImportExportLoaded state) {
    final totalSelected =
        state.selectedCustomerContacts.length +
        state.selectedSupplierContacts.length;
    final isEnabled = totalSelected > 0;

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isEnabled
            ? () {
                HapticFeedback.lightImpact();
                context.read<ImportExportBloc>().add(
                  const CreatePartiesFromContacts(),
                );
              }
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isEnabled
              ? const Color(0xFF5A67D8)
              : Colors.grey[300],
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: AppText(
          totalSelected > 0
              ? 'Create Parties ($totalSelected)'
              : 'Create Parties',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: isEnabled ? Colors.white : Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildSuccessView(BuildContext context, int createdCount) {
    return Center(
      child: AnimationConfiguration.staggeredList(
        position: 0,
        duration: const Duration(milliseconds: 500),
        child: ScaleAnimation(
          scale: 0.8,
          curve: Curves.easeOutBack,
          child: FadeInAnimation(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 40),
                ),
                const SizedBox(height: 24),
                AppText(
                  'Success!',
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                const SizedBox(height: 8),
                AppText(
                  '$createdCount parties created successfully',
                  fontSize: 16,
                  color: Colors.grey[600],
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF5A67D8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const AppText(
                    'Done',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToContactSelection(BuildContext context, String type) {
    final bloc = context.read<ImportExportBloc>();
    final currentState = bloc.state;
    List<String> initialSelectedContacts = [];

    if (currentState is ImportExportLoaded) {
      initialSelectedContacts = type == 'Customer'
          ? currentState.selectedCustomerContacts
          : currentState.selectedSupplierContacts;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => BlocProvider.value(
        value: bloc,
        child: ContactSelectionBottomSheet(
          contactType: type,
          initialSelectedContacts: initialSelectedContacts,
        ),
      ),
    );
  }
}
