import 'package:equatable/equatable.dart';

class ContactItem extends Equatable {
  final String id;
  final String name;
  final String? phoneNumber;
  final String? email;
  final String? company;

  const ContactItem({
    required this.id,
    required this.name,
    this.phoneNumber,
    this.email,
    this.company,
  });

  @override
  List<Object?> get props => [id, name, phoneNumber, email, company];

  ContactItem copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    String? company,
  }) {
    return ContactItem(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      company: company ?? this.company,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'company': company,
    };
  }

  factory ContactItem.fromJson(Map<String, dynamic> json) {
    return ContactItem(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      company: json['company'] as String?,
    );
  }
}
