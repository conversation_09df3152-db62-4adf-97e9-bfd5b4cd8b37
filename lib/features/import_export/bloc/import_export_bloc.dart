import 'package:flutter_bloc/flutter_bloc.dart';
import 'import_export_event.dart';
import 'import_export_state.dart';
import '../models/contact_item.dart';
import 'package:bill_book/core/services/contact_service.dart';

class ImportExportBloc extends Bloc<ImportExportEvent, ImportExportState> {
  ImportExportBloc() : super(const ImportExportInitial()) {
    on<LoadImportExport>(_onLoadImportExport);
    on<SelectCustomerContacts>(_onSelectCustomerContacts);
    on<SelectSupplierContacts>(_onSelectSupplierContacts);
    on<CreatePartiesFromContacts>(_onCreatePartiesFromContacts);
    on<BulkAddParties>(_onBulkAddParties);
    on<LoadDeviceContacts>(_onLoadDeviceContacts);
    on<SearchContacts>(_onSearchContacts);
  }

  // Mock contact data - in real app, this would come from device contacts
  final List<ContactItem> _mockContacts = [
    const ContactItem(
      id: '1',
      name: '<PERSON>',
      phoneNumber: '+1234567890',
      email: '<EMAIL>',
    ),
    const ContactItem(
      id: '2',
      name: '<PERSON>',
      phoneNumber: '+1234567891',
      email: '<EMAIL>',
    ),
    const ContactItem(
      id: '3',
      name: 'Mike Wilson',
      phone<PERSON>umber: '+1234567892',
      email: '<EMAIL>',
    ),
    const ContactItem(
      id: '4',
      name: 'Emily Davis',
      phoneNumber: '+1234567893',
      email: '<EMAIL>',
    ),
    const ContactItem(
      id: '5',
      name: 'David Brown',
      phoneNumber: '+1234567894',
      email: '<EMAIL>',
    ),
  ];

  Future<void> _onLoadImportExport(
    LoadImportExport event,
    Emitter<ImportExportState> emit,
  ) async {
    emit(const ImportExportLoading());

    try {
      // Simulate loading contacts from device
      await Future.delayed(const Duration(milliseconds: 500));

      emit(ImportExportLoaded(availableContacts: _mockContacts));
    } catch (e) {
      emit(ImportExportError('Failed to load contacts: ${e.toString()}'));
    }
  }

  Future<void> _onSelectCustomerContacts(
    SelectCustomerContacts event,
    Emitter<ImportExportState> emit,
  ) async {
    if (state is ImportExportLoaded) {
      final currentState = state as ImportExportLoaded;
      emit(
        currentState.copyWith(selectedCustomerContacts: event.selectedContacts),
      );
    }
  }

  Future<void> _onSelectSupplierContacts(
    SelectSupplierContacts event,
    Emitter<ImportExportState> emit,
  ) async {
    if (state is ImportExportLoaded) {
      final currentState = state as ImportExportLoaded;
      emit(
        currentState.copyWith(selectedSupplierContacts: event.selectedContacts),
      );
    }
  }

  Future<void> _onCreatePartiesFromContacts(
    CreatePartiesFromContacts event,
    Emitter<ImportExportState> emit,
  ) async {
    if (state is ImportExportLoaded) {
      final currentState = state as ImportExportLoaded;

      try {
        emit(const ImportExportLoading());

        // Simulate creating parties from selected contacts
        await Future.delayed(const Duration(milliseconds: 1000));

        final totalCreated =
            currentState.selectedCustomerContacts.length +
            currentState.selectedSupplierContacts.length;

        emit(PartiesCreated(totalCreated));
      } catch (e) {
        emit(ImportExportError('Failed to create parties: ${e.toString()}'));
      }
    }
  }

  Future<void> _onBulkAddParties(
    BulkAddParties event,
    Emitter<ImportExportState> emit,
  ) async {
    // Handle bulk add parties functionality
    // This would typically open a file picker or import dialog
    print('Bulk add parties requested');
  }

  Future<void> _onLoadDeviceContacts(
    LoadDeviceContacts event,
    Emitter<ImportExportState> emit,
  ) async {
    if (state is ImportExportLoaded) {
      final currentState = state as ImportExportLoaded;

      try {
        emit(currentState.copyWith(isLoadingContacts: true));

        final deviceContacts = await ContactService.getContacts();

        emit(
          currentState.copyWith(
            deviceContacts: deviceContacts,
            isLoadingContacts: false,
          ),
        );
      } catch (e) {
        emit(
          ImportExportError('Failed to load device contacts: ${e.toString()}'),
        );
      }
    }
  }

  Future<void> _onSearchContacts(
    SearchContacts event,
    Emitter<ImportExportState> emit,
  ) async {
    if (state is ImportExportLoaded) {
      final currentState = state as ImportExportLoaded;

      final filteredContacts = ContactService.searchContacts(
        currentState.deviceContacts,
        event.query,
      );

      emit(currentState.copyWith(deviceContacts: filteredContacts));
    }
  }
}
