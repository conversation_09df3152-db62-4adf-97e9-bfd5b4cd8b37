import 'package:equatable/equatable.dart';

abstract class ImportExportEvent extends Equatable {
  const ImportExportEvent();

  @override
  List<Object?> get props => [];
}

class LoadImportExport extends ImportExportEvent {
  const LoadImportExport();
}

class SelectCustomerContacts extends ImportExportEvent {
  final List<String> selectedContacts;

  const SelectCustomerContacts(this.selectedContacts);

  @override
  List<Object?> get props => [selectedContacts];
}

class SelectSupplierContacts extends ImportExportEvent {
  final List<String> selectedContacts;

  const SelectSupplierContacts(this.selectedContacts);

  @override
  List<Object?> get props => [selectedContacts];
}

class CreatePartiesFromContacts extends ImportExportEvent {
  const CreatePartiesFromContacts();
}

class BulkAddParties extends ImportExportEvent {
  const BulkAddParties();
}

class LoadDeviceContacts extends ImportExportEvent {
  const LoadDeviceContacts();
}

class SearchContacts extends ImportExportEvent {
  final String query;

  const SearchContacts(this.query);

  @override
  List<Object?> get props => [query];
}
