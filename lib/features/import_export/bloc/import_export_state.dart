import 'package:equatable/equatable.dart';
import '../models/contact_item.dart';

abstract class ImportExportState extends Equatable {
  const ImportExportState();

  @override
  List<Object?> get props => [];
}

class ImportExportInitial extends ImportExportState {
  const ImportExportInitial();
}

class ImportExportLoading extends ImportExportState {
  const ImportExportLoading();
}

class ImportExportLoaded extends ImportExportState {
  final List<ContactItem> availableContacts;
  final List<ContactItem> deviceContacts;
  final List<String> selectedCustomerContacts;
  final List<String> selectedSupplierContacts;
  final bool isLoadingContacts;

  const ImportExportLoaded({
    required this.availableContacts,
    this.deviceContacts = const [],
    this.selectedCustomerContacts = const [],
    this.selectedSupplierContacts = const [],
    this.isLoadingContacts = false,
  });

  @override
  List<Object?> get props => [
    availableContacts,
    deviceContacts,
    selectedCustomerContacts,
    selectedSupplierContacts,
    isLoadingContacts,
  ];

  ImportExportLoaded copyWith({
    List<ContactItem>? availableContacts,
    List<ContactItem>? deviceContacts,
    List<String>? selectedCustomerContacts,
    List<String>? selectedSupplierContacts,
    bool? isLoadingContacts,
  }) {
    return ImportExportLoaded(
      availableContacts: availableContacts ?? this.availableContacts,
      deviceContacts: deviceContacts ?? this.deviceContacts,
      selectedCustomerContacts:
          selectedCustomerContacts ?? this.selectedCustomerContacts,
      selectedSupplierContacts:
          selectedSupplierContacts ?? this.selectedSupplierContacts,
      isLoadingContacts: isLoadingContacts ?? this.isLoadingContacts,
    );
  }
}

class ImportExportError extends ImportExportState {
  final String message;

  const ImportExportError(this.message);

  @override
  List<Object?> get props => [message];
}

class PartiesCreated extends ImportExportState {
  final int createdCount;

  const PartiesCreated(this.createdCount);

  @override
  List<Object?> get props => [createdCount];
}
