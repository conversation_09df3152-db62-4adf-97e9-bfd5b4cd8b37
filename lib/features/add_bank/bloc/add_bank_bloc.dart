import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import 'add_bank_event.dart';
import 'add_bank_state.dart';
import '../models/bank_account.dart';

class AddBankBloc extends Bloc<AddBankEvent, AddBankState> {
  AddBankBloc() : super(AddBankState()) {
    on<AccountDisplayNameChanged>(_onAccountDisplayNameChanged);
    on<OpeningBalanceChanged>(_onOpeningBalanceChanged);
    on<AsOfDateChanged>(_onAsOfDateChanged);
    on<ToggleBankAccountDetails>(_onToggleBankAccountDetails);
    on<AccountHolderNameChanged>(_onAccountHolderNameChanged);
    on<BankAccountNumberChanged>(_onBankAccountNumberChanged);
    on<ConfirmBankAccountNumberChanged>(_onConfirmBankAccountNumberChanged);
    on<IfscCodeChanged>(_onIfscCodeChanged);
    on<UpiIdChanged>(_onUpiIdChanged);
    on<SaveBankAccount>(_onSaveBankAccount);
    on<ResetForm>(_onResetForm);
  }

  void _onAccountDisplayNameChanged(
    AccountDisplayNameChanged event,
    Emitter<AddBankState> emit,
  ) {
    emit(
      state.copyWith(
        accountDisplayName: event.accountDisplayName,
        fieldErrors: Map.from(state.fieldErrors)..remove('accountDisplayName'),
      ),
    );
  }

  void _onOpeningBalanceChanged(
    OpeningBalanceChanged event,
    Emitter<AddBankState> emit,
  ) {
    emit(
      state.copyWith(
        openingBalance: event.openingBalance,
        fieldErrors: Map.from(state.fieldErrors)..remove('openingBalance'),
      ),
    );
  }

  void _onAsOfDateChanged(AsOfDateChanged event, Emitter<AddBankState> emit) {
    emit(state.copyWith(asOfDate: event.asOfDate));
  }

  void _onToggleBankAccountDetails(
    ToggleBankAccountDetails event,
    Emitter<AddBankState> emit,
  ) {
    emit(state.copyWith(showBankAccountDetails: !state.showBankAccountDetails));
  }

  void _onAccountHolderNameChanged(
    AccountHolderNameChanged event,
    Emitter<AddBankState> emit,
  ) {
    emit(
      state.copyWith(
        accountHolderName: event.accountHolderName,
        fieldErrors: Map.from(state.fieldErrors)..remove('accountHolderName'),
      ),
    );
  }

  void _onBankAccountNumberChanged(
    BankAccountNumberChanged event,
    Emitter<AddBankState> emit,
  ) {
    emit(
      state.copyWith(
        bankAccountNumber: event.bankAccountNumber,
        fieldErrors: Map.from(state.fieldErrors)
          ..remove('bankAccountNumber')
          ..remove('confirmBankAccountNumber'),
      ),
    );
  }

  void _onConfirmBankAccountNumberChanged(
    ConfirmBankAccountNumberChanged event,
    Emitter<AddBankState> emit,
  ) {
    emit(
      state.copyWith(
        confirmBankAccountNumber: event.confirmBankAccountNumber,
        fieldErrors: Map.from(state.fieldErrors)
          ..remove('confirmBankAccountNumber'),
      ),
    );
  }

  void _onIfscCodeChanged(IfscCodeChanged event, Emitter<AddBankState> emit) {
    emit(
      state.copyWith(
        ifscCode: event.ifscCode.toUpperCase(),
        fieldErrors: Map.from(state.fieldErrors)..remove('ifscCode'),
      ),
    );
  }

  void _onUpiIdChanged(UpiIdChanged event, Emitter<AddBankState> emit) {
    emit(state.copyWith(upiId: event.upiId));
  }

  Future<void> _onSaveBankAccount(
    SaveBankAccount event,
    Emitter<AddBankState> emit,
  ) async {
    // Validate form
    final errors = <String, String>{};

    if (state.accountDisplayName.isEmpty) {
      errors['accountDisplayName'] = 'Account display name is required';
    }

    if (state.openingBalance.isEmpty) {
      errors['openingBalance'] = 'Opening balance is required';
    } else {
      final balance = double.tryParse(state.openingBalance);
      if (balance == null) {
        errors['openingBalance'] = 'Please enter a valid amount';
      } else if (balance < 0) {
        errors['openingBalance'] = 'Opening balance cannot be negative';
      }
    }

    if (state.showBankAccountDetails) {
      if (state.accountHolderName.isEmpty) {
        errors['accountHolderName'] = 'Account holder name is required';
      }

      if (state.bankAccountNumber.isEmpty) {
        errors['bankAccountNumber'] = 'Bank account number is required';
      }

      if (state.confirmBankAccountNumber.isEmpty) {
        errors['confirmBankAccountNumber'] =
            'Please confirm bank account number';
      } else if (state.bankAccountNumber != state.confirmBankAccountNumber) {
        errors['confirmBankAccountNumber'] = 'Account numbers do not match';
      }

      if (state.ifscCode.isEmpty) {
        errors['ifscCode'] = 'IFSC code is required';
      } else if (!_isValidIfscCode(state.ifscCode)) {
        errors['ifscCode'] = 'Please enter a valid IFSC code';
      }
    }

    if (errors.isNotEmpty) {
      emit(state.copyWith(fieldErrors: errors));
      return;
    }

    emit(state.copyWith(status: AddBankStatus.loading));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 1500));

      // Create bank account
      final bankAccount = BankAccount(
        id: const Uuid().v4(),
        accountDisplayName: state.accountDisplayName,
        openingBalance: double.parse(state.openingBalance),
        asOfDate: state.asOfDate,
        accountHolderName: state.showBankAccountDetails
            ? state.accountHolderName
            : '',
        bankAccountNumber: state.showBankAccountDetails
            ? state.bankAccountNumber
            : '',
        ifscCode: state.showBankAccountDetails ? state.ifscCode : '',
        upiId: state.showBankAccountDetails && state.upiId.isNotEmpty
            ? state.upiId
            : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: Save to repository/API
      print('Bank account created: ${bankAccount.toString()}');

      emit(state.copyWith(status: AddBankStatus.success));
    } catch (e) {
      emit(
        state.copyWith(
          status: AddBankStatus.failure,
          errorMessage: 'Failed to save bank account: ${e.toString()}',
        ),
      );
    }
  }

  void _onResetForm(ResetForm event, Emitter<AddBankState> emit) {
    emit(AddBankState(asOfDate: DateTime.now()));
  }

  bool _isValidIfscCode(String ifscCode) {
    // Basic IFSC code validation: 4 letters followed by 7 digits
    final ifscRegex = RegExp(r'^[A-Z]{4}[0-9]{7}$');
    return ifscRegex.hasMatch(ifscCode.toUpperCase());
  }
}
