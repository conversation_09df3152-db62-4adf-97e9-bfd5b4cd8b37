import 'package:equatable/equatable.dart';

enum AddBankStatus { initial, loading, success, failure }

class AddBankState extends Equatable {
  final AddBankStatus status;
  final String accountDisplayName;
  final String openingBalance;
  final DateTime asOfDate;
  final bool showBankAccountDetails;
  final String accountHolderName;
  final String bankAccountNumber;
  final String confirmBankAccountNumber;
  final String ifscCode;
  final String upiId;
  final String? errorMessage;
  final Map<String, String> fieldErrors;

  AddBankState({
    this.status = AddBankStatus.initial,
    this.accountDisplayName = '',
    this.openingBalance = '',
    DateTime? asOfDate,
    this.showBankAccountDetails = false,
    this.accountHolderName = '',
    this.bankAccountNumber = '',
    this.confirmBankAccountNumber = '',
    this.ifscCode = '',
    this.upiId = '',
    this.errorMessage,
    this.fieldErrors = const {},
  }) : asOfDate = asOfDate ?? DateTime.now();

  AddBankState copyWith({
    AddBankStatus? status,
    String? accountDisplayName,
    String? openingBalance,
    DateTime? asOfDate,
    bool? showBankAccountDetails,
    String? accountHolderName,
    String? bankAccountNumber,
    String? confirmBankAccountNumber,
    String? ifscCode,
    String? upiId,
    String? errorMessage,
    Map<String, String>? fieldErrors,
  }) {
    return AddBankState(
      status: status ?? this.status,
      accountDisplayName: accountDisplayName ?? this.accountDisplayName,
      openingBalance: openingBalance ?? this.openingBalance,
      asOfDate: asOfDate ?? this.asOfDate,
      showBankAccountDetails:
          showBankAccountDetails ?? this.showBankAccountDetails,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      confirmBankAccountNumber:
          confirmBankAccountNumber ?? this.confirmBankAccountNumber,
      ifscCode: ifscCode ?? this.ifscCode,
      upiId: upiId ?? this.upiId,
      errorMessage: errorMessage,
      fieldErrors: fieldErrors ?? this.fieldErrors,
    );
  }

  bool get isFormValid {
    return accountDisplayName.isNotEmpty &&
        openingBalance.isNotEmpty &&
        _isValidOpeningBalance &&
        (!showBankAccountDetails || _areBankDetailsValid);
  }

  bool get _isValidOpeningBalance {
    final balance = double.tryParse(openingBalance);
    return balance != null && balance >= 0;
  }

  bool get _areBankDetailsValid {
    return accountHolderName.isNotEmpty &&
        bankAccountNumber.isNotEmpty &&
        confirmBankAccountNumber.isNotEmpty &&
        bankAccountNumber == confirmBankAccountNumber &&
        ifscCode.isNotEmpty &&
        _isValidIfscCode;
  }

  bool get _isValidIfscCode {
    // Basic IFSC code validation: 4 letters followed by 7 digits
    final ifscRegex = RegExp(r'^[A-Z]{4}[0-9]{7}$');
    return ifscRegex.hasMatch(ifscCode.toUpperCase());
  }

  bool get accountNumbersMatch {
    return bankAccountNumber.isNotEmpty &&
        confirmBankAccountNumber.isNotEmpty &&
        bankAccountNumber == confirmBankAccountNumber;
  }

  @override
  List<Object?> get props => [
    status,
    accountDisplayName,
    openingBalance,
    asOfDate,
    showBankAccountDetails,
    accountHolderName,
    bankAccountNumber,
    confirmBankAccountNumber,
    ifscCode,
    upiId,
    errorMessage,
    fieldErrors,
  ];
}
