import 'package:equatable/equatable.dart';

abstract class AddBankEvent extends Equatable {
  const AddBankEvent();

  @override
  List<Object?> get props => [];
}

class AccountDisplayNameChanged extends AddBankEvent {
  final String accountDisplayName;

  const AccountDisplayNameChanged(this.accountDisplayName);

  @override
  List<Object?> get props => [accountDisplayName];
}

class OpeningBalanceChanged extends AddBankEvent {
  final String openingBalance;

  const OpeningBalanceChanged(this.openingBalance);

  @override
  List<Object?> get props => [openingBalance];
}

class AsOfDateChanged extends AddBankEvent {
  final DateTime asOfDate;

  const AsOfDateChanged(this.asOfDate);

  @override
  List<Object?> get props => [asOfDate];
}

class ToggleBankAccountDetails extends AddBankEvent {
  const ToggleBankAccountDetails();
}

class AccountHolderNameChanged extends AddBankEvent {
  final String accountHolderName;

  const AccountHolderNameChanged(this.accountHolderName);

  @override
  List<Object?> get props => [accountHolderName];
}

class BankAccountNumberChanged extends AddBankEvent {
  final String bankAccountNumber;

  const BankAccountNumberChanged(this.bankAccountNumber);

  @override
  List<Object?> get props => [bankAccountNumber];
}

class ConfirmBankAccountNumberChanged extends AddBankEvent {
  final String confirmBankAccountNumber;

  const ConfirmBankAccountNumberChanged(this.confirmBankAccountNumber);

  @override
  List<Object?> get props => [confirmBankAccountNumber];
}

class IfscCodeChanged extends AddBankEvent {
  final String ifscCode;

  const IfscCodeChanged(this.ifscCode);

  @override
  List<Object?> get props => [ifscCode];
}

class UpiIdChanged extends AddBankEvent {
  final String upiId;

  const UpiIdChanged(this.upiId);

  @override
  List<Object?> get props => [upiId];
}

class SaveBankAccount extends AddBankEvent {
  const SaveBankAccount();
}

class ResetForm extends AddBankEvent {
  const ResetForm();
}
