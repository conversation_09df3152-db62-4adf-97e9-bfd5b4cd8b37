import 'package:equatable/equatable.dart';

class BankAccount extends Equatable {
  final String id;
  final String accountDisplayName;
  final double openingBalance;
  final DateTime asOfDate;
  final String accountHolderName;
  final String bankAccountNumber;
  final String ifscCode;
  final String? upiId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BankAccount({
    required this.id,
    required this.accountDisplayName,
    required this.openingBalance,
    required this.asOfDate,
    required this.accountHolderName,
    required this.bankAccountNumber,
    required this.ifscCode,
    this.upiId,
    required this.createdAt,
    required this.updatedAt,
  });

  BankAccount copyWith({
    String? id,
    String? accountDisplayName,
    double? openingBalance,
    DateTime? asOfDate,
    String? accountHolderName,
    String? bankAccountNumber,
    String? ifscCode,
    String? upiId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      accountDisplayName: accountDisplayName ?? this.accountDisplayName,
      openingBalance: openingBalance ?? this.openingBalance,
      asOfDate: asOfDate ?? this.asOfDate,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      ifscCode: ifscCode ?? this.ifscCode,
      upiId: upiId ?? this.upiId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountDisplayName': accountDisplayName,
      'openingBalance': openingBalance,
      'asOfDate': asOfDate.toIso8601String(),
      'accountHolderName': accountHolderName,
      'bankAccountNumber': bankAccountNumber,
      'ifscCode': ifscCode,
      'upiId': upiId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory BankAccount.fromJson(Map<String, dynamic> json) {
    return BankAccount(
      id: json['id'] as String,
      accountDisplayName: json['accountDisplayName'] as String,
      openingBalance: (json['openingBalance'] as num).toDouble(),
      asOfDate: DateTime.parse(json['asOfDate'] as String),
      accountHolderName: json['accountHolderName'] as String,
      bankAccountNumber: json['bankAccountNumber'] as String,
      ifscCode: json['ifscCode'] as String,
      upiId: json['upiId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  List<Object?> get props => [
        id,
        accountDisplayName,
        openingBalance,
        asOfDate,
        accountHolderName,
        bankAccountNumber,
        ifscCode,
        upiId,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'BankAccount(id: $id, accountDisplayName: $accountDisplayName, openingBalance: $openingBalance, asOfDate: $asOfDate, accountHolderName: $accountHolderName, bankAccountNumber: $bankAccountNumber, ifscCode: $ifscCode, upiId: $upiId, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
