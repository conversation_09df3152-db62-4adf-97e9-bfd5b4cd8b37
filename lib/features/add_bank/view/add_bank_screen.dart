import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/widgets/date_picker_bottom_sheet.dart';
import '../bloc/add_bank_bloc.dart';
import '../bloc/add_bank_event.dart';
import '../bloc/add_bank_state.dart';
import '../widgets/add_bank_form_fields.dart';

class AddBankScreen extends StatefulWidget {
  const AddBankScreen({super.key});

  @override
  State<AddBankScreen> createState() => _AddBankScreenState();
}

class _AddBankScreenState extends State<AddBankScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late TextEditingController _accountDisplayNameController;
  late TextEditingController _openingBalanceController;
  late TextEditingController _accountHolderNameController;
  late TextEditingController _bankAccountNumberController;
  late TextEditingController _confirmBankAccountNumberController;
  late TextEditingController _ifscCodeController;
  late TextEditingController _upiIdController;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _accountDisplayNameController = TextEditingController();
    _openingBalanceController = TextEditingController();
    _accountHolderNameController = TextEditingController();
    _bankAccountNumberController = TextEditingController();
    _confirmBankAccountNumberController = TextEditingController();
    _ifscCodeController = TextEditingController();
    _upiIdController = TextEditingController();

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _accountDisplayNameController.dispose();
    _openingBalanceController.dispose();
    _accountHolderNameController.dispose();
    _bankAccountNumberController.dispose();
    _confirmBankAccountNumberController.dispose();
    _ifscCodeController.dispose();
    _upiIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AddBankBloc(),
      child: BlocListener<AddBankBloc, AddBankState>(
        listener: (context, state) {
          if (state.status == AddBankStatus.success) {
            HapticFeedback.mediumImpact();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: AppText(
                  'Bank account added successfully!',
                  color: Colors.white,
                  fontSize: 14,
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
            Navigator.of(context).pop(true);
          } else if (state.status == AddBankStatus.failure) {
            HapticFeedback.heavyImpact();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: AppText(
                  state.errorMessage ?? 'Failed to add bank account',
                  color: Colors.white,
                  fontSize: 14,
                ),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: _buildAppBar(),
          body: SafeArea(child: _buildBody()),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.black87),
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
      ),
      title: const AppText(
        'Add New Bank',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
      centerTitle: false,
    );
  }

  Widget _buildBody() {
    return BlocBuilder<AddBankBloc, AddBankState>(
      builder: (context, state) {
        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: AnimationLimiter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 400),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        verticalOffset: 30.0,
                        curve: Curves.easeOutCubic,
                        child: FadeInAnimation(
                          curve: Curves.easeOutCubic,
                          child: widget,
                        ),
                      ),
                      children: [
                        _buildBasicInfoSection(context, state),
                        const SizedBox(height: 24),
                        _buildBankDetailsToggle(context, state),
                        if (state.showBankAccountDetails) ...[
                          const SizedBox(height: 24),
                          _buildBankDetailsSection(context, state),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
            AddBankFormFields.buildSaveButton(context, state),
          ],
        );
      },
    );
  }

  Widget _buildBasicInfoSection(BuildContext context, AddBankState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAccountDisplayNameField(context, state),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildOpeningBalanceField(context, state)),
              const SizedBox(width: 16),
              Expanded(child: _buildAsOfDateField(context, state)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountDisplayNameField(
    BuildContext context,
    AddBankState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(
            text: 'Account Display Name',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _accountDisplayNameController,
          onChanged: (value) {
            context.read<AddBankBloc>().add(AccountDisplayNameChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'Ex: ICICI Bank',
            hintStyle: TextStyle(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: state.fieldErrors['accountDisplayName'],
          ),
        ),
      ],
    );
  }

  Widget _buildOpeningBalanceField(BuildContext context, AddBankState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Opening Balance',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _openingBalanceController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          onChanged: (value) {
            context.read<AddBankBloc>().add(OpeningBalanceChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'Ex: ₹ 3,000',
            hintStyle: TextStyle(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: state.fieldErrors['openingBalance'],
          ),
        ),
      ],
    );
  }

  Widget _buildAsOfDateField(BuildContext context, AddBankState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'As of Date',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            HapticFeedback.lightImpact();
            final bloc = context.read<AddBankBloc>();
            final selectedDate = await showModalBottomSheet<DateTime>(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) => DatePickerBottomSheet(
                initialDate: state.asOfDate,
                firstDate: DateTime(2000),
                lastDate: DateTime.now().add(const Duration(days: 365)),
              ),
            );
            if (selectedDate != null && mounted) {
              bloc.add(AsOfDateChanged(selectedDate));
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    DateFormat('dd MMM yyyy').format(state.asOfDate),
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const Icon(
                  Icons.calendar_today,
                  color: Color(0xFF5A67D8),
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBankDetailsToggle(BuildContext context, AddBankState state) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        context.read<AddBankBloc>().add(const ToggleBankAccountDetails());
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Expanded(
              child: AppText(
                'Add bank account details',
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Switch.adaptive(
              value: state.showBankAccountDetails,
              onChanged: (value) {
                HapticFeedback.lightImpact();
                context.read<AddBankBloc>().add(
                  const ToggleBankAccountDetails(),
                );
              },
              activeColor: const Color(0xFF5A67D8),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankDetailsSection(BuildContext context, AddBankState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAccountHolderNameField(context, state),
          const SizedBox(height: 16),
          AddBankFormFields.buildBankAccountNumberField(
            context,
            state,
            _bankAccountNumberController,
          ),
          const SizedBox(height: 16),
          AddBankFormFields.buildConfirmBankAccountNumberField(
            context,
            state,
            _confirmBankAccountNumberController,
          ),
          const SizedBox(height: 16),
          AddBankFormFields.buildIfscCodeField(
            context,
            state,
            _ifscCodeController,
          ),
          const SizedBox(height: 16),
          AddBankFormFields.buildUpiIdField(context, state, _upiIdController),
        ],
      ),
    );
  }

  Widget _buildAccountHolderNameField(
    BuildContext context,
    AddBankState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(
            text: 'Account Holder\'s Name',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _accountHolderNameController,
          onChanged: (value) {
            context.read<AddBankBloc>().add(AccountHolderNameChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'Ex: Ankit Mishra',
            hintStyle: TextStyle(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8)),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: state.fieldErrors['accountHolderName'],
          ),
        ),
      ],
    );
  }
}
